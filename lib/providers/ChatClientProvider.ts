import { Conversation<PERSON><PERSON>ider } from "./types"

const chatClientBaseURL = process.env.CHAT_CLIENT_BASE_URL!

export class ChatClientProvider implements ConversationProvider {
  async syncSession(sessionId: string): Promise<{
    success: boolean
    status?: string
    me?: any
    providerData?: any
    error?: string
  }> {
    return {
      success: true,
      status: "CONNECTED",
      me: {
        name: "<PERSON>",
        phone: "6281234567890",
      },
      providerData: {
        lastSync: new Date().toISOString(),
      },
    }
  }

  name: string = "ChatClientProvider"

  async createSession(name: string): Promise<string> {
    return "fake-session-id"
  }

  async restartSession(sessionId: string): Promise<string> {
    return "fake-session-id"
  }

  async startSession(sessionId: string): Promise<string> {
    return "fake-session-id"
  }

  async stopSession(sessionId: string): Promise<string> {
    return "fake-session-id"
  }

  async logoutSession(sessionId: string): Promise<string> {
    return "fake-session-id"
  }

  async getQr(sessionId?: string): Promise<string> {
    return "fake-session-id"
  }

  async listDevices(): Promise<any> {
    return []
  }

  async sendPresenceStatus(presence: string, sessionId?: string) {}

  async sendMessage(
    conversationId: string,
    text: string,
    session: string,
  ): Promise<string> {
    await fetch(`${chatClientBaseURL}/api/external/sendMessage`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        conversationId,
        text,
        sessionId: session,
      }),
    })
    return "sent"
  }

  async sendTyping(
    status: string,
    conversationId: string,
    sessionId?: string,
  ) {}
}
