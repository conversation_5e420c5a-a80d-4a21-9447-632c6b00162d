import { BroadcastProvider } from "../types"

// Helper to look up BSP auth/session info for a session ID
async function getBspSessionData(sessionId: string): Promise<{
  token: string
  baseUrl: string
  phoneNumber: string
}> {
  // This would pull from your DB, cache, or session manager
  return {
    token: "SESSION-TOKEN-FROM-LOGIN", // typically from /login
    baseUrl: "http://bsp.example.com", // your BSP API base
    phoneNumber: "**********", // optional, depends on routing
  }
}

export const BspBroadcastProvider: BroadcastProvider = {
  name: "bsp",

  async sendBroadcast(
    customerId: string,
    text: string,
    sessionId: string,
  ): Promise<void> {
    const { token, baseUrl } = await getBspSessionData(sessionId)

    const apiUrl = `${baseUrl}/v1/messages`

    const payload = {
      to: customerId,
      type: "template",
      template: {
        namespace: "your_namespace_id", // defined in your BSP config
        name: "hello_world",
        language: {
          policy: "deterministic",
          code: "en_US",
        },
        components: [], // or pass parameters here
      },
    }

    const res = await fetch(apiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })

    if (!res.ok) {
      const error = await res.json()
      console.error("BSP broadcast failed:", error)
      throw new Error(`BSP broadcast failed: ${res.statusText}`)
    }

    console.log(`BSP broadcast sent to ${customerId}`)
  },
}
