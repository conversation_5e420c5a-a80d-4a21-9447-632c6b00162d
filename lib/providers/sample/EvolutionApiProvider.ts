import axios, { AxiosInstance } from "axios"
import { ConversationProvider } from "../types"

const EV_API_BASE_URL = process.env.EVOLUTIONAPI_BASE_URL!
const EV_API_KEY = process.env.EVOLUTIONAPI_API_KEY! // or whatever key or auth type it uses

const client: AxiosInstance = axios.create({
  baseURL: EV_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${EV_API_KEY}`,
  },
})

export class EvolutionApiProvider implements ConversationProvider {
  name = "EvolutionAPI"

  async createSession(name: string): Promise<string> {
    const res = await client.post("/instance", { name })
    const sessionId = res.data?.instanceId
    if (!sessionId) throw new Error("EvolutionAPI did not return instanceId.")
    return sessionId
  }

  async restartSession(sessionId: string): Promise<string> {
    const res = await client.post(`/instance/${sessionId}/restart`)
    if (!res.data)
      throw new Error(`<PERSON>AP<PERSON> failed to restart instance ${sessionId}.`)
    return res.data
  }

  async startSession(sessionId: string): Promise<string> {
    const res = await client.post(`/instance/${sessionId}/start`)
    if (!res.data)
      throw new Error(`EvolutionAPI failed to start instance ${sessionId}.`)
    return res.data
  }

  async stopSession(sessionId: string): Promise<string> {
    const res = await client.post(`/instance/${sessionId}/stop`)
    if (!res.data)
      throw new Error(`EvolutionAPI failed to stop instance ${sessionId}.`)
    return res.data
  }

  async logoutSession(sessionId: string): Promise<string> {
    const res = await client.post(`/instance/${sessionId}/logout`)
    if (!res.data)
      throw new Error(`EvolutionAPI failed to logout instance ${sessionId}.`)
    return res.data
  }

  async getQr(sessionId?: string): Promise<string> {
    if (!sessionId)
      throw new Error("sessionId is required to get QR from EvolutionAPI.")
    const res = await client.get(`/instance/${sessionId}/qr`)
    const qr = res.data?.qr
    if (!qr) throw new Error("QR not found in EvolutionAPI response.")
    return qr
  }

  async listDevices(): Promise<any> {
    const res = await client.get("/instances")
    return res.data
  }

  async sendPresenceStatus(
    presence: string,
    sessionId?: string,
  ): Promise<void> {
    if (!sessionId)
      throw new Error("sessionId is required to send presence in EvolutionAPI.")
    await client.post(`/instance/${sessionId}/presence`, { presence })
  }

  async sendMessage(
    conversationId: string,
    text: string,
    session: string,
  ): Promise<string> {
    if (!session)
      throw new Error(
        "Session (phone/instance) is required to send message in EvolutionAPI.",
      )
    if (!text)
      throw new Error("Text is required to send message in EvolutionAPI.")
    if (!conversationId)
      throw new Error(
        "ConversationId is required to send message in EvolutionAPI.",
      )

    const payload = {
      session,
      conversationId,
      text,
    }
    const res = await client.post("/message/send", payload)
    if (!res.data) throw new Error("EvolutionAPI sendMessage returned no data.")
    return res.data
  }

  async sendTyping(
    status: string,
    conversationId: string,
    sessionId?: string,
  ): Promise<void> {
    if (!sessionId)
      throw new Error("sessionId is required to send typing in EvolutionAPI.")
    if (!conversationId)
      throw new Error(
        "ConversationId is required to send typing in EvolutionAPI.",
      )
    if (!status) throw new Error("Typing status is required (start or stop).")

    await client.post(status === "start" ? `/typing/start` : `/typing/stop`, {
      session: sessionId,
      conversationId,
    })
  }

  async syncSession(sessionId: string): Promise<{
    success: boolean
    status?: string
    me?: any
    providerData?: any
    error?: string
  }> {
    if (!sessionId) {
      return {
        success: false,
        error: "sessionId is required to sync in EvolutionAPI.",
      }
    }
    try {
      const res = await client.get(`/instance/${sessionId}`)
      const sessionData = res.data
      return {
        success: true,
        status: sessionData.status,
        me: sessionData.me,
        providerData: {
          lastSync: new Date().toISOString(),
          raw: sessionData,
        },
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || "Failed to sync session in EvolutionAPI.",
      }
    }
  }
}
