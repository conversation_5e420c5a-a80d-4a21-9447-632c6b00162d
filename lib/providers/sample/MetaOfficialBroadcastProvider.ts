import { BroadcastProvider } from "../types"

const META_PHONE_NUMBER_ID = process.env.META_PHONE_NUMBER_ID // WhatsApp Business phone number ID
const META_ACCESS_TOKEN = process.env.META_ACCESS_TOKEN // Permanent or short-lived token
const META_API_URL = `https://graph.facebook.com/v19.0/${META_PHONE_NUMBER_ID}/messages`

export const MetaBroadcastProvider: BroadcastProvider = {
  name: "meta",

  async sendBroadcast(
    customerId: string,
    text: string,
    session: string,
  ): Promise<void> {
    // You must have a pre-approved template and use the format WhatsA<PERSON> expects.
    // For this example, we'll assume a "hello_world" template with no variables.

    const payload = {
      messaging_product: "whatsapp",
      to: customerId, // Must be in E.164 format: e.g. "6281234567890"
      type: "template",
      template: {
        name: "hello_world", // Your pre-approved template name here
        language: {
          code: "en_US",
        },
      },
    }

    const res = await fetch(META_API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${META_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })

    if (!res.ok) {
      const errorBody = await res.json()
      console.error("Failed to send broadcast:", errorBody)
      throw new Error(`Meta broadcast failed: ${res.statusText}`)
    }

    console.log(`Broadcast message sent to ${customerId}`)
  },
}
