import axios, { AxiosInstance } from "axios"
import { ConversationProvider } from "../types"

const GOWA_BASE_URL = process.env.GOWA_BASE_URL!
const GOWA_USERNAME = process.env.GOWA_USERNAME!
const GOWA_PASSWORD = process.env.GOWA_PASSWORD!

const client: AxiosInstance = axios.create({
  baseURL: GOWA_BASE_URL,
  headers: { "Content-Type": "application/json" },
})

const authHeader = {
  Authorization:
    "Basic " +
    Buffer.from(`${GOWA_USERNAME}:${GOWA_PASSWORD}`).toString("base64"),
}

export class GowaProvider implements ConversationProvider {
  name = "Gowa API"

  async logout() {
    const res = await client.get(`/app/logout`, {
      headers: authHeader,
    })
    if (!res) throw new Error(`Gowa API logout failed.`)
    return res.data?.results
  }

  async getQr() {
    const res = await client.get("/app/login", {
      headers: authHeader,
    })

    const qr = res.data?.results?.qr_link
    if (!qr) throw new Error("QR code not found in Gowa API response.")
    return qr
  }

  async listDevices() {
    const res = await client.get("/app/devices", {
      headers: authHeader,
    })

    return res.data?.results
  }

  async sendMessage(conversationId: string, text: string, session: string) {
    if (!session)
      throw new Error("Session (phone number) is required to send a message.")
    if (!text) throw new Error("Message text is required.")
    if (!conversationId)
      throw new Error("Conversation ID is required to send a message.")

    const payload = {
      phone: session,
      message: text,
      reply_message_id: conversationId,
      is_forwarded: false,
    }

    const res = await client.post(`/sendText`, payload, {
      headers: authHeader,
    })

    return res.data
  }

  async sendTyping(status: string, conversationId: string, sessionId?: string) {
    throw new Error("Gowa API does not support sendTyping.")
  }

  async createSession(name: string): Promise<any> {
    throw new Error("Gowa API does not support createSession.")
  }

  async startSession(sessionId: string): Promise<any> {
    throw new Error("Gowa API does not support startSession.")
  }

  async stopSession(sessionId: string): Promise<any> {
    throw new Error("Gowa API does not support stopSession.")
  }

  async restartSession(sessionId: string): Promise<any> {
    throw new Error("Gowa API does not support restartSession.")
  }

  async logoutSession(sessionId: string): Promise<any> {
    return this.logout()
  }

  async sendPresenceStatus(presence: string, sessionId?: string): Promise<any> {
    throw new Error("Gowa API does not support sendPresenceStatus.")
  }

  async syncSession(sessionId: string): Promise<any> {
    return {
      success: true,
      status: "UNKNOWN",
      providerData: {
        lastSync: new Date().toISOString(),
        message: "Gowa does not support session sync.",
      },
    }
  }
}
