import { Provider } from "./types";
//import { Client } from "twilio";

const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID!;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN!;
const TWILIO_WHATSAPP_NUMBER = process.env.TWILIO_WHATSAPP_NUMBER!; // e.g., 'whatsapp:+***********'


// const twilioClient = new Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
const twilioClient: any = {};

export class TwilioProvider implements Provider {
    name = "Twilio API";

    async sendMessage(
        conversationId: string,
        text: string,
        session: string
    ) {
        if (!session) throw new Error("Recipient phone number (session) is required.");
        if (!text) throw new Error("Message text is required.");
        if (!conversationId) throw new Error("Conversation ID is required (can be same as session).");

        const to = session.startsWith("whatsapp:") ? session : `whatsapp:${session}`;

        const message = await twilioClient.messages.create({
            from: TWILIO_WHATSAPP_NUMBER,
            to,
            body: text,
        });

        return message.sid;
    }

    async getQr() {
        return "no-qr"
    }

    async createSession(name: string): Promise<any> {
        throw new Error("Twilio does not support session creation.");
    }

    async startSession(sessionId: string): Promise<any> {
        throw new Error("Twilio does not support starting sessions.");
    }

    async stopSession(sessionId: string): Promise<any> {
        throw new Error("Twilio does not support stopping sessions.");
    }

    async restartSession(sessionId: string): Promise<any> {
        throw new Error("Twilio does not support restarting sessions.");
    }

    async logoutSession(sessionId: string): Promise<any> {
        throw new Error("Twilio does not support logging out sessions.");
    }

    async sendTyping(
        status: string,
        conversationId: string,
        sessionId?: string
    ) {
        throw new Error("Twilio does not support sendTyping.");
    }

    async sendPresenceStatus(presence: string, sessionId?: string): Promise<any> {
        throw new Error("Twilio does not support presence updates.");
    }

    async listDevices(): Promise<any> {
        throw new Error("Twilio does not provide device listing for WhatsApp.");
    }

    async logout(): Promise<any> {
        throw new Error("Twilio does not support logout.");
    }

    async syncSession(sessionId: string): Promise<any> {
        return {
            success: true,
            status: "N/A",
            providerData: {
                lastSync: new Date().toISOString(),
                message: "Twilio does not support session sync.",
            },
        };
    }
}
