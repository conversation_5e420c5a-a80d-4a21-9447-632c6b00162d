import axios, { AxiosInstance } from "axios";
import { Provider } from "./types";

const WAHA_WEBHOOK_URL = process.env.WAHA_WEBHOOK_URL!;
const INTERNAL_SECRET_TOKEN = process.env.INTERNAL_SECRET_TOKEN!;
const WAHA_API_URL = process.env.WAHA_API_URL!
const WAHA_API_KEY = process.env.WAHA_API_KEY!

export class Waha<PERSON>rovider implements Provider {
  name = "WAHA API";
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: WAHA_API_URL + "/api",
      headers: {
        "X-Api-Key": WAHA_API_KEY,
        "Content-Type": "application/json",
      },
    });
  }

  async createSession(name: string) {
    const res = await this.client.post("/sessions", {
      name,
      start: true,
      config: {
        noweb: {
          store: {
            enabled: true,
            fullSync: true,
          },
        },
        webhooks: !WAHA_WEBHOOK_URL
          ? []
          : [
            {
              url: `${WAHA_WEBHOOK_URL}`,
              events: ["message", "session.status"],
              retries: {
                policy: "constant",
                delaySeconds: 2,
                attempts: 2
              },
              customHeaders: [
                {
                  name: "X-Internal-System-Token",
                  value: INTERNAL_SECRET_TOKEN,
                },
              ],
            },
            {
              url: `${WAHA_WEBHOOK_URL}/presence`,
              events: ["presence.update"],
              retries: {
                policy: "constant",
                delaySeconds: 2,
                attempts: 2
              },
              customHeaders: [
                {
                  name: "X-Internal-System-Token",
                  value: INTERNAL_SECRET_TOKEN,
                },
              ],
            },
          ],
      },
    });

    const sessionId = res.data?.name;
    if (!sessionId) throw new Error("WAHA API did not return a sessionId.");
    return sessionId;
  }

  async startSession(sessionId: string) {
    const res = await this.client.post(`/sessions/${sessionId}/start`, {});
    if (!res) throw new Error(`WAHA API failed to start session ${sessionId}.`);
    return res.data;
  }

  async stopSession(sessionId: string) {
    const res = await this.client.post(`/sessions/${sessionId}/stop`, {});
    if (!res) throw new Error(`WAHA API failed to stop session ${sessionId}.`);
    return res.data;
  }

  async restartSession(sessionId: string) {
    const res = await this.client.post(`/sessions/${sessionId}/restart`, {});
    if (!res) throw new Error(`WAHA API failed to restart session ${sessionId}.`);
    return res.data;
  }

  async logoutSession(sessionId: string) {
    if (!sessionId) throw new Error("SessionId is required to logout WAHA.");

    const res = await this.client.delete(`/sessions/${sessionId}`, {});
    if (!res) throw new Error(`WAHA API failed to logout session ${sessionId}.`);
    return res.data;
  }

  async getQr(sessionId?: string) {
    if (!sessionId) throw new Error("SessionId is required to retrieve QR code from WAHA.");

    const res = await this.client.get(`/${sessionId}/auth/qr`, {
      responseType: "arraybuffer",
    });

    const contentType = res.headers["content-type"];
    if (!contentType) throw new Error("Content-Type not found in WAHA response.");

    const base64Image = Buffer.from(res.data).toString("base64");

    if (!base64Image) throw new Error("QR code not found in WAHA response.");

    return `data:${contentType};base64,${base64Image}`;
  }

  async listDevices() {
    const res = await this.client.get("/sessions");
    return res.data;
  }

  async sendPresenceStatus(presence: string, sessionId?: string) {
    if (!sessionId) throw new Error("SessionId is required to send presence status.");
    await this.client.post(`/${sessionId}/presence`, { presence });
  }

  async sendMessage(conversationId: string, text: string, session: string) {
    if (!conversationId) throw new Error("ConversationId is required to send a message.");
    if (!text) throw new Error("Text is required to send a message.");
    if (!session) throw new Error("Session is required to send a message.");

    const payload = {
      chatId: conversationId,
      text,
      session,
      reply_to: null,
      linkPreview: true,
      linkPreviewHighQuality: false,
    };

    const res = await this.client.post(`/sendText`, payload);
    return res.data;
  }

  async sendTyping(status: string, conversationId: string, sessionId?: string) {
    if (!status) throw new Error("Typing status is required (start or stop).");
    if (!sessionId) throw new Error("SessionId is required for sendTyping.");
    if (!conversationId) throw new Error("ConversationId is required for sendTyping.");

    await this.client.post(
      status === "start" ? `/startTyping` : "/stopTyping",
      {
        conversationId,
        session: sessionId,
      }
    );
  }

  async syncSession(sessionId: string) {
    if (!sessionId) {
      return {
        success: false,
        error: "SessionId is required to sync session.",
      };
    }

    try {
      // Get session status from WAHA
      const statusRes = await this.client.get(`/sessions/${sessionId}`);
      const sessionData = statusRes.data;

      // Try to get "me" data from WAHA
      let meData = null;
      try {
        const meRes = await this.client.get(`/sessions/${sessionId}/me`);
        meData = meRes.data;
      } catch (meError) {
        console.warn(`Could not retrieve 'me' data for session ${sessionId}:`, meError);
      }

      return {
        success: true,
        status: sessionData.status || "UNKNOWN",
        me: meData,
        providerData: {
          lastSync: new Date().toISOString(),
          wahaStatus: sessionData,
        },
      };
    } catch (error: any) {
      console.error(`Error syncing session ${sessionId}:`, error);

      if (error.response?.status === 404 || error.response?.status === 400) {
        return {
          success: true,
          status: "DISCONNECTED",
          error: "Session not found or is disconnected.",
        };
      }

      return {
        success: false,
        error: error.message || "Failed to sync session.",
      };
    }
  }
}
