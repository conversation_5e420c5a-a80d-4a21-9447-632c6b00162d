// src/lib/providers/WuziProvider.ts
import axios, { AxiosInstance } from "axios";
import { Provider } from "./types";

const WUZAPI_BASE_URL = process.env.WUZAPI_BASE_URL!;
const WUZAPI_TOKEN = process.env.WUZAPI_TOKEN!;

const client: AxiosInstance = axios.create({
    baseURL: WUZAPI_BASE_URL,
    headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${WUZAPI_TOKEN}`,
    },
});

export class WuzApiProvider implements Provider {
    name = "<PERSON>z<PERSON><PERSON>";

    async createSession(name: string): Promise<string> {
        // WuzAPI's "session" is referred to as "instance" or "user" in their API?  
        // Checking the endpoints: WuzAPI has endpoints to connect/disconnect sessions and QR codes. :contentReference[oaicite:1]{index=1}

        // Suppose the endpoint is: POST /instances or POST /users or /session depending on API.
        const res = await client.post("/instances", { name });
        const sessionId = res.data?.instanceName || res.data?.instanceId || res.data?.userId;
        if (!sessionId) {
            throw new Error("WuzAPI did not return a session (instance) identifier.");
        }
        return sessionId;
    }

    async restartSession(sessionId: string): Promise<string> {
        const res = await client.post(`/instance/${sessionId}/restart`);
        if (!res.data) {
            throw new Error(`WuzAPI failed to restart session ${sessionId}.`);
        }
        return sessionId;
    }

    async startSession(sessionId: string): Promise<string> {
        const res = await client.post(`/instance/${sessionId}/connect`);
        if (!res.data) {
            throw new Error(`WuzAPI failed to start/connect session ${sessionId}.`);
        }
        return sessionId;
    }

    async stopSession(sessionId: string): Promise<string> {
        const res = await client.post(`/instance/${sessionId}/disconnect`);
        if (!res.data) {
            throw new Error(`WuzAPI failed to stop/disconnect session ${sessionId}.`);
        }
        return sessionId;
    }

    async logoutSession(sessionId: string): Promise<string> {
        const res = await client.post(`/instance/${sessionId}/logout`);
        if (!res.data) {
            throw new Error(`WuzAPI failed to logout session ${sessionId}.`);
        }
        return sessionId;
    }

    async getQr(sessionId?: string): Promise<string> {
        if (!sessionId) throw new Error("sessionId is required to get QR from WuzAPI.");

        const res = await client.get(`/instance/${sessionId}/qr`);
        const qr = res.data?.qr || res.data?.qr_link;
        if (!qr) {
            throw new Error("QR code not found in WuzAPI response.");
        }
        return qr;
    }

    async listDevices(): Promise<any> {
        // List all sessions / instances
        const res = await client.get("/instances");
        return res.data;
    }

    async sendPresenceStatus(presence: string, sessionId?: string): Promise<void> {
        if (!sessionId) throw new Error("sessionId is required to send presence status with WuzAPI.");

        await client.post(`/instance/${sessionId}/presence`, { presence });
    }

    async sendMessage(
        conversationId: string,
        text: string,
        session: string
    ): Promise<string> {
        if (!session) throw new Error("Session (instance or phone) is required to send message via WuzAPI.");
        if (!text) throw new Error("Text is required to send message via WuzAPI.");
        if (!conversationId) throw new Error("ConversationId is required to send message via WuzAPI.");

        const payload = {
            instance: session,
            to: conversationId,
            message: text,
        };
        const res = await client.post("/messages/text", payload);
        if (!res.data) {
            throw new Error("WuzAPI sendMessage returned no data.");
        }
        // Could return messageId
        return res.data.messageId || res.data.id || "unknown";
    }

    async sendTyping(
        status: string,
        conversationId: string,
        sessionId?: string
    ): Promise<void> {
        if (!sessionId) throw new Error("sessionId is required for sendTyping with WuzAPI.");
        if (!conversationId) throw new Error("ConversationId is required for sendTyping with WuzAPI.");
        if (status !== "start" && status !== "stop") {
            throw new Error("Typing status must be 'start' or 'stop'.");
        }

        await client.post(`/instance/${sessionId}/typing`, {
            to: conversationId,
            status,
        });
    }

    async syncSession(sessionId: string): Promise<{
        success: boolean;
        status?: string;
        me?: any;
        providerData?: any;
        error?: string;
    }> {
        if (!sessionId) {
            return { success: false, error: "sessionId is required for syncSession (WuzAPI)." };
        }
        try {
            const res = await client.get(`/instance/${sessionId}`);
            const sessionData = res.data;
            return {
                success: true,
                status: sessionData.status,
                me: sessionData?.user,
                providerData: {
                    lastSync: new Date().toISOString(),
                    raw: sessionData,
                },
            };
        } catch (err: any) {
            return {
                success: false,
                error: err.message || "Failed to sync session with WuzAPI.",
            };
        }
    }
}
