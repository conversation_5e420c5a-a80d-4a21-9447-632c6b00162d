import { BaseAPI } from "./baseApi"
import {
  LibraryTemplate,
  LibraryTemplateQueryParams,
  LibraryTemplateInstallResult,
} from "@/lib/repositories/library/interface"

export interface LibraryTemplateListResponse {
  items: LibraryTemplate[]
  total: number
  page: number
  limit: number
}

export interface LibraryTemplateSearchResponse {
  items: LibraryTemplate[]
  total: number
}

export interface LibraryTemplateInstallRequest {
  templateId: string
  customizations?: Record<string, any>
}

export class LibraryAPI extends BaseAPI {
  static GetAll = (params?: LibraryTemplateQueryParams) => {
    const queryParams = new URLSearchParams()

    if (params?.search) queryParams.append("search", params.search)
    if (params?.type) queryParams.append("type", params.type)
    if (params?.category) queryParams.append("category", params.category)
    if (params?.tags?.length) queryParams.append("tags", params.tags.join(","))
    if (params?.isActive !== undefined)
      queryParams.append("isActive", String(params.isActive))
    if (params?.page) queryParams.append("page", String(params.page))
    if (params?.limit) queryParams.append("limit", String(params.limit))
    if (params?.sortBy) queryParams.append("sortBy", params.sortBy)
    if (params?.sortOrder) queryParams.append("sortOrder", params.sortOrder)

    const queryString = queryParams.toString()
      ? `?${queryParams.toString()}`
      : ""

    return new BaseAPI(
      `/library${queryString}`,
    ).build<LibraryTemplateListResponse>()
  }

  static GetById = (id: string) =>
    new BaseAPI(`/library/${id}`).build<LibraryTemplate>()

  static Search = (
    query: string,
    params?: Omit<LibraryTemplateQueryParams, "search">,
  ) => {
    const queryParams = new URLSearchParams()
    queryParams.append("q", query)

    if (params?.type) queryParams.append("type", params.type)
    if (params?.category) queryParams.append("category", params.category)
    if (params?.tags?.length) queryParams.append("tags", params.tags.join(","))
    if (params?.page) queryParams.append("page", String(params.page))
    if (params?.limit) queryParams.append("limit", String(params.limit))
    if (params?.sortBy) queryParams.append("sortBy", params.sortBy)
    if (params?.sortOrder) queryParams.append("sortOrder", params.sortOrder)

    return new BaseAPI(
      `/library/search?${queryParams.toString()}`,
    ).build<LibraryTemplateSearchResponse>()
  }

  static Install = (body: LibraryTemplateInstallRequest) =>
    new BaseAPI(
      `/library/install`,
      body,
      "POST",
    ).build<LibraryTemplateInstallResult>()
}
