// src/lib/queue/index.ts
import { RabbitMQService } from "./RabbitMQService"
import { InternalQueue } from "./InternalQueue"
import { MessageQueue } from "./MessageQueue"
import { getQueueConfig } from "@/lib/config/queue"

let instance: MessageQueue | null = null

export function getMessageQueue(): MessageQueue {
  if (!instance) {
    const queueConfig = getQueueConfig()

    // Use InternalQueue if RabbitMQ URL is not provided
    if (!queueConfig.rabbitmqUrl) {
      console.warn("🔄 RabbitMQ URL not provided, using InternalQueue")
      instance = new InternalQueue()
    } else {
      instance = new RabbitMQService(queueConfig.rabbitmqUrl)
    }

    instance.connect()
  }
  return instance
}
