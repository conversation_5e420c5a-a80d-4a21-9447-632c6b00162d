// src/lib/queue/index.ts
import { RabbitMQService } from "./RabbitMQService"
import { MessageQueue } from "./MessageQueue"
import { getQueueConfig } from "@/lib/config/queue"

let instance: MessageQueue | null = null

export function getMessageQueue(): MessageQueue {
    if (!instance) {
        const queueConfig = getQueueConfig()
        instance = new RabbitMQService(queueConfig.rabbitmqUrl)
        instance.connect()
    }
    return instance
}
