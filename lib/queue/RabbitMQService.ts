import amqp, { Channel } from "amqplib"
import { MessageQueue } from "./MessageQueue"

export class RabbitMQService implements MessageQueue {
  private connection: any | null = null
  private channel: Channel | null = null
  private readonly url: string

  constructor(url: string) {
    this.url = url
  }

  async connect(): Promise<void> {
    try {
      this.connection = await amqp.connect(this.url)
      this.channel = await this.connection.createChannel()

      this.connection.on("error", (err: any) => {
        console.error("RabbitMQ connection error:", err)
      })

      this.connection.on("close", () => {
        console.log("RabbitMQ connection closed")
        this.connection = null
        this.channel = null
      })

      console.log("✅ Connected to RabbitMQ")
    } catch (error) {
      console.error("❌ Failed to connect to RabbitMQ:", error)
      throw error
    }
  }

  async enqueue(queueName: string, payload: any): Promise<void> {
    if (!this.channel) {
      throw new Error("RabbitMQ channel not initialized. Call connect() first.")
    }

    try {
      await this.channel.checkQueue(queueName)

      const message = Buffer.from(JSON.stringify(payload))
      const sent = this.channel.sendToQueue(queueName, message, { persistent: true })

      if (!sent) {
        throw new Error("Failed to send message to queue")
      }

      console.log(`📤 Message enqueued to ${queueName}:`, payload.type || "unknown")
    } catch (error) {
      console.error(`❌ Error enqueuing message to ${queueName}:`, error)
      throw error
    }
  }


  async getQueueInfo(queueName: string): Promise<{ messageCount: number; consumerCount: number }> {
    if (!this.channel) {
      throw new Error("RabbitMQ channel not initialized. Call connect() first.")
    }

    try {
      const queueInfo = await this.channel.checkQueue(queueName)
      return {
        messageCount: queueInfo.messageCount,
        consumerCount: queueInfo.consumerCount,
      }
    } catch (error) {
      console.error(`❌ Error getting queue info for ${queueName}:`, error)
      throw error
    }
  }
}
