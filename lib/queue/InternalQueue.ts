import { MessageQueue } from "./MessageQueue"

export class InternalQueue implements MessageQueue {
  private isConnected = false

  async connect(): Promise<void> {
    this.isConnected = true
    console.log("📡 InternalQueue connected (in-memory processing)")
  }

  async enqueue(queueName: string, payload: any): Promise<void> {
    if (!this.isConnected) {
      throw new Error("InternalQueue not connected. Call connect() first.")
    }

    console.log(
      `📤 Processing message internally for ${queueName}:`,
      payload.type || "unknown",
    )

    // Process immediately by calling the appropriate handler
    if (queueName === "broadcast.send") {
      await this.processBroadcastSend(payload)
    } else {
      console.warn(`⚠️ Unknown queue: ${queueName}`)
    }
  }

  private async processBroadcastSend(payload: any): Promise<void> {
    try {
      const response = await fetch(payload.callbackUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Internal-System-Token": payload.internalSystemToken,
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      console.log(`✅ Broadcast message processed successfully`)
    } catch (error) {
      console.error(`❌ Failed to process broadcast message:`, error)
      throw error
    }
  }
}
