// Support configuration interfaces
export interface SupportAction {
  type: "email" | "link" | "phone"
  target: string
  subject?: string
}

export interface SupportChannel {
  id: string
  name: string
  description: string
  icon: string
  color: string
  responseTime: string
  availability: string
  recommended: boolean
  action: SupportAction
}

export interface QuickAction {
  id: string
  title: string
  description: string
  icon: string
  action: SupportAction
}

export interface EmergencyContact {
  available: boolean
  contact: {
    whatsapp: string
  }
}

export interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

export interface SupportConfig {
  supportChannels: SupportChannel[]
  quickActions: QuickAction[]
  emergencyContact: EmergencyContact
  faq: FAQItem[]
  tips: string[]
}
