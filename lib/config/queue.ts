export interface QueueConfig {
  rabbitmqUrl: string | null
  isDevelopment: boolean
  retryAttempts: number
  retryDelay: number
}

export function getQueueConfig(): QueueConfig {
  const isDevelopment = process.env.NODE_ENV === "development"
  return {
    rabbitmqUrl: process.env.RABBITMQ_URL || null,
    isDevelopment,
    retryAttempts: parseInt(process.env.QUEUE_RETRY_ATTEMPTS || "3"),
    retryDelay: parseInt(process.env.QUEUE_RETRY_DELAY || "1000"),
  }
}

export function getCallbackUrl(): string {
  return process.env.BROADCAST_SENDER_CALLBACK_URL!
}

export function getHealthCheckConfig() {
  return {
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT!),
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL!),
  }
}
