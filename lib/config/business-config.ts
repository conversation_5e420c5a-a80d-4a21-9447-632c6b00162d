import { promises as fs } from "fs"
import path from "path"

// Business configuration interfaces
export interface BusinessContact {
  email: string
  phone: string
  whatsapp: string
  whatsappLink: string
  website: string
  address: string
}

export interface BusinessHours {
  timezone: string
  weekdays: {
    start: string
    end: string
  }
  weekends: {
    start: string
    end: string
  }
}

export interface BusinessInfo {
  name: string
  logo?: string
  description?: string
  tagline?: string
  primaryColor?: string
  secondaryColor?: string
}

export interface BusinessConfig {
  businessInfo: BusinessInfo
  contact: BusinessContact
  businessHours: BusinessHours
}

// Cache for fallback configurations
let fallbackConfigCache: Record<string, BusinessConfig> = {}

// Cache for loaded configurations
let configCache: Record<string, BusinessConfig> = {}

/**
 * Load fallback configuration from JSON file
 */
async function loadFallbackConfig(locale: string): Promise<BusinessConfig> {
  // Check cache first
  if (fallbackConfigCache[locale]) {
    return fallbackConfigCache[locale]
  }

  try {
    // Try to load the specific locale fallback
    const fallbackPath = path.join(
      process.cwd(),
      "lib",
      "config",
      "defaults",
      `business-config-${locale}.json`,
    )
    const fallbackData = await fs.readFile(fallbackPath, "utf-8")
    const fallbackConfig = JSON.parse(fallbackData) as BusinessConfig

    if (isValidBusinessConfig(fallbackConfig)) {
      fallbackConfigCache[locale] = fallbackConfig
      return fallbackConfig
    }
  } catch (error) {
    console.log(
      `Fallback config not found for locale ${locale}, trying English`,
    )
  }

  // If specific locale not found, try English
  if (locale !== "en") {
    try {
      const fallbackPath = path.join(
        process.cwd(),
        "lib",
        "config",
        "defaults",
        "business-config-en.json",
      )
      const fallbackData = await fs.readFile(fallbackPath, "utf-8")
      const fallbackConfig = JSON.parse(fallbackData) as BusinessConfig

      if (isValidBusinessConfig(fallbackConfig)) {
        fallbackConfigCache[locale] = fallbackConfig
        return fallbackConfig
      }
    } catch (error) {
      console.error("Failed to load English fallback config:", error)
    }
  }

  // Last resort: hardcoded minimal config
  const minimalConfig: BusinessConfig = {
    businessInfo: {
      name: "CS AI",
      description: "AI-powered customer service platform",
      tagline: "Intelligent Customer Support",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+62 851-9005-2577",
      whatsapp: "+62 851-9005-2577",
      whatsappLink: "https://wa.me/6285190052577",
      website: "https://cspintar.com",
      address: "Jakarta, Indonesia",
    },
    businessHours: {
      timezone: "Asia/Jakarta",
      weekdays: {
        start: "09:00",
        end: "18:00",
      },
      weekends: {
        start: "10:00",
        end: "16:00",
      },
    },
  }

  fallbackConfigCache[locale] = minimalConfig
  return minimalConfig
}

/**
 * Load business configuration from mounted volume or fallback to default
 */
export async function loadBusinessConfig(
  locale: string = "en",
): Promise<BusinessConfig> {
  // Check cache first
  if (configCache[locale]) {
    return configCache[locale]
  }

  try {
    // Try to load from mounted volume first
    const volumePath = path.join("/app/data", `business-config-${locale}.json`)
    const configData = await fs.readFile(volumePath, "utf-8")
    const config = JSON.parse(configData) as BusinessConfig

    // Validate the config structure
    if (isValidBusinessConfig(config)) {
      configCache[locale] = config
      return config
    } else {
      console.warn(
        `Invalid business config structure in ${volumePath}, using fallback`,
      )
    }
  } catch (error) {
    // If file doesn't exist or can't be read, try fallback
    console.log(
      `Business config not found for locale ${locale}, using fallback`,
    )
  }

  // Load fallback configuration from JSON file
  const fallback = await loadFallbackConfig(locale)
  configCache[locale] = fallback
  return fallback
}

/**
 * Validate business configuration structure
 */
function isValidBusinessConfig(config: any): config is BusinessConfig {
  return (
    config &&
    typeof config === "object" &&
    config.businessInfo &&
    typeof config.businessInfo.name === "string" &&
    config.contact &&
    typeof config.contact.email === "string" &&
    typeof config.contact.phone === "string" &&
    config.businessHours &&
    typeof config.businessHours.timezone === "string" &&
    config.businessHours.weekdays &&
    typeof config.businessHours.weekdays.start === "string" &&
    typeof config.businessHours.weekdays.end === "string"
  )
}

/**
 * Clear configuration cache (useful for testing or reloading)
 */
export function clearBusinessConfigCache(): void {
  configCache = {}
  fallbackConfigCache = {}
}
