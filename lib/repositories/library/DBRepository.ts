import { Collection } from "mongodb"
import {
  LibraryTemplate,
  LibraryTemplateCreateInput,
  LibraryTemplateUpdateInput,
  LibraryTemplateQueryParams,
} from "./interface"
import { MongoDriver } from "../MongoDriver"
import { generateId } from "@/lib/utils/common"

export class LibraryTemplateDBRepository {
  private collection: Collection<LibraryTemplate>

  constructor(client: MongoDriver, dbName: string) {
    this.collection = client.getCollection<LibraryTemplate>("library_templates")
  }

  async getById(id: string): Promise<LibraryTemplate | null> {
    try {
      const result = await this.collection.findOne({
        id,
        isActive: true,
      })

      if (!result) return null
      return this.transformFromDB(result)
    } catch (error) {
      console.error("Error getting library template by ID:", error)
      return null
    }
  }

  async getAll(params: LibraryTemplateQueryParams): Promise<{
    items: LibraryTemplate[]
    total: number
    page: number
    limit: number
  }> {
    try {
      const {
        search,
        type,
        category,
        tags,
        isActive = true,
        page = 1,
        limit = 20,
        sortBy = "createdAt",
        sortOrder = "DESC",
      } = params

      const filter: any = { isActive }
      if (type) filter.type = type
      if (category) filter.category = category
      if (tags && tags.length > 0) {
        filter.tags = { $in: tags }
      }

      if (search) {
        filter.$or = [
          { title: { $regex: search, $options: "i" } },
          { description: { $regex: search, $options: "i" } },
          { tags: { $regex: search, $options: "i" } },
        ]
      }

      const sort: any = {}
      sort[sortBy] = sortOrder === "ASC" ? 1 : -1

      const skip = (page - 1) * limit
      const [items, total] = await Promise.all([
        this.collection
          .find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .toArray(),
        this.collection.countDocuments(filter),
      ])

      return {
        items: items.map((item) => this.transformFromDB(item)),
        total,
        page,
        limit,
      }
    } catch (error) {
      console.error("Error getting all library templates:", error)
      return { items: [], total: 0, page: 1, limit: 20 }
    }
  }

  async create(data: LibraryTemplateCreateInput): Promise<LibraryTemplate> {
    try {
      const now = new Date()
      const id = generateId()

      const templateData: LibraryTemplate = {
        ...data,
        id,
        isActive: true,
        downloadCount: 0,
        rating: 0,
        createdAt: now,
        updatedAt: now,
        version: data.version || "1.0.0",
        createdBy: "system",
      }

      await this.collection.insertOne(templateData)

      return this.transformFromDB(templateData)
    } catch (error) {
      console.error("Error creating library template:", error)
      throw error
    }
  }

  async update(
    id: string,
    data: LibraryTemplateUpdateInput,
  ): Promise<LibraryTemplate | null> {
    try {
      const updateData = {
        ...data,
        updatedAt: new Date(),
      }

      const result = await this.collection.findOneAndUpdate(
        { id },
        { $set: updateData },
        { returnDocument: "after" },
      )

      if (!result) return null
      return this.transformFromDB(result)
    } catch (error) {
      console.error("Error updating library template:", error)
      return null
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.collection.updateOne(
        { id },
        {
          $set: {
            isActive: false,
            updatedAt: new Date(),
          },
        },
      )

      return result.modifiedCount > 0
    } catch (error) {
      console.error("Error deleting library template:", error)
      return false
    }
  }

  async incrementDownloadCount(id: string): Promise<void> {
    try {
      await this.collection.updateOne(
        { id },
        {
          $inc: { downloadCount: 1 },
          $set: { updatedAt: new Date() },
        },
      )
    } catch (error) {
      console.error("Error incrementing download count:", error)
    }
  }

  async updateRating(id: string, rating: number): Promise<void> {
    try {
      await this.collection.updateOne(
        { id },
        {
          $set: {
            rating,
            updatedAt: new Date(),
          },
        },
      )
    } catch (error) {
      console.error("Error updating rating:", error)
    }
  }

  private transformFromDB(doc: any): LibraryTemplate {
    return {
      id: doc.id,
      title: doc.title,
      description: doc.description,
      type: doc.type,
      category: doc.category,
      tags: doc.tags || [],
      content: doc.content || {},
      preview: doc.preview,
      isActive: doc.isActive,
      downloadCount: doc.downloadCount || 0,
      rating: doc.rating || 0,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      createdBy: doc.createdBy,
      version: doc.version || "1.0.0",
      metadata: doc.metadata,
    }
  }

  async createIndexes(): Promise<void> {
    try {
      await Promise.all([
        this.collection.createIndex({ id: 1 }, { unique: true }),
        this.collection.createIndex({ type: 1 }),
        this.collection.createIndex({ category: 1 }),
        this.collection.createIndex({ tags: 1 }),
        this.collection.createIndex({ isActive: 1 }),
        this.collection.createIndex({ downloadCount: -1 }),
        this.collection.createIndex({ rating: -1 }),
        this.collection.createIndex({ createdAt: -1 }),
        this.collection.createIndex({
          title: "text",
          description: "text",
          tags: "text",
        }),
      ])
    } catch (error) {
      console.error("Error creating indexes:", error)
    }
  }
}
