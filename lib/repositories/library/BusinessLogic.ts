import {
  LibraryTemplate,
  LibraryTemplateCreateInput,
  LibraryTemplateUpdateInput,
  LibraryTemplateQueryParams,
  LibraryTemplateInstallInput,
  LibraryTemplateInstallResult,
  LibraryTemplateBusinessLogicInterface,
  MessageTemplateContent,
  AiRuleContent,
  DatasourceContent,
  KnowledgeBaseContent,
} from "./interface"
import { LibraryTemplateDBRepository } from "./DBRepository"
import { SessionContext } from "../auth/types"
import { MessageTemplateBusinessLogicInterface } from "../messageTemplates/interface"
import { AiRuleBusinessLogicInterface } from "../aiRules/interface"
import { DatasourceBusinessLogicInterface } from "../datasources/interface"
import { KnowledgeBaseBusinessLogicInterface } from "../knowledgeBase/interface"

export class LibraryTemplateBusinessLogic
  implements LibraryTemplateBusinessLogicInterface
{
  constructor(
    private db: LibraryTemplateDBRepository,
    private messageTemplateBusinessLogic: MessageTemplateBusinessLogicInterface,
    private aiRuleBusinessLogic: AiRuleBusinessLogicInterface,
    private datasourceBusinessLogic: DatasourceBusinessLogicInterface,
    private knowledgeBaseBusinessLogic: KnowledgeBaseBusinessLogicInterface,
  ) {}

  async getById(id: string): Promise<LibraryTemplate | null> {
    return this.db.getById(id)
  }

  async getAll(params: LibraryTemplateQueryParams): Promise<{
    items: LibraryTemplate[]
    total: number
    page: number
    limit: number
  }> {
    return this.db.getAll(params)
  }

  async search(
    query: string,
    params?: LibraryTemplateQueryParams,
  ): Promise<{
    items: LibraryTemplate[]
    total: number
  }> {
    const searchParams = {
      ...params,
      search: query,
      page: params?.page || 1,
      limit: params?.limit || 20,
    }

    const result = await this.db.getAll(searchParams)
    return {
      items: result.items,
      total: result.total,
    }
  }

  async install(
    input: LibraryTemplateInstallInput,
    context: SessionContext,
  ): Promise<LibraryTemplateInstallResult> {
    try {
      // Get the template
      const template = await this.getById(input.templateId)
      if (!template) {
        return {
          success: false,
          installedId: "",
          type: "message_template",
          message: "Template not found",
        }
      }

      // Increment download count
      await this.incrementDownloadCount(input.templateId)

      let installedId: string
      let message: string

      // Install based on template type
      switch (template.type) {
        case "message_template":
          installedId = await this.installMessageTemplate(
            template,
            input.customizations,
            context,
          )
          message = "Message template installed successfully"
          break

        case "ai_rule":
          installedId = await this.installAiRule(
            template,
            input.customizations,
            context,
          )
          message = "AI rule installed successfully"
          break

        case "datasource":
          installedId = await this.installDatasource(
            template,
            input.customizations,
            context,
          )
          message = "Datasource installed successfully"
          break

        case "knowledge_base":
          installedId = await this.installKnowledgeBase(
            template,
            input.customizations,
            context,
          )
          message = "Knowledge base entry installed successfully"
          break

        default:
          return {
            success: false,
            installedId: "",
            type: template.type,
            message: "Unsupported template type",
          }
      }

      return {
        success: true,
        installedId,
        type: template.type,
        message,
      }
    } catch (error) {
      console.error("Error installing template:", error)
      return {
        success: false,
        installedId: "",
        type: "message_template",
        message: error instanceof Error ? error.message : "Installation failed",
      }
    }
  }

  private async installMessageTemplate(
    template: LibraryTemplate,
    customizations: Record<string, any> = {},
    context: SessionContext,
  ): Promise<string> {
    const content = template.content as MessageTemplateContent

    const messageTemplateData = {
      title: customizations.title || content.title,
      query: customizations.query || content.query,
      template: customizations.template || content.template,
      variables: customizations.variables || content.variables || [],
      tags: customizations.tags || template.tags,
      isActive: true,
    }

    const created = await this.messageTemplateBusinessLogic.create(
      messageTemplateData,
      context,
    )
    return created.id
  }

  private async installAiRule(
    template: LibraryTemplate,
    customizations: Record<string, any> = {},
    context: SessionContext,
  ): Promise<string> {
    const content = template.content as AiRuleContent

    const aiRuleData = {
      name: customizations.name || content.name,
      description: customizations.description || content.description,
      conditions: customizations.conditions || content.conditions,
      actions: customizations.actions || content.actions,
      tags: customizations.tags || template.tags,
      isActive: true,
    }

    const created = await this.aiRuleBusinessLogic.create(aiRuleData, context)
    return created.id
  }

  private async installDatasource(
    template: LibraryTemplate,
    customizations: Record<string, any> = {},
    context: SessionContext,
  ): Promise<string> {
    const content = template.content as DatasourceContent

    const datasourceData = {
      name: customizations.name || content.name,
      type: customizations.type || content.type,
      url: customizations.url || content.url,
      content: customizations.content || content.content,
      accessKey: customizations.accessKey || content.accessKey,
      isActive: true,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const created = await this.datasourceBusinessLogic.create(
      datasourceData,
      context,
    )
    return created.id
  }

  private async installKnowledgeBase(
    template: LibraryTemplate,
    customizations: Record<string, any> = {},
    context: SessionContext,
  ): Promise<string> {
    const content = template.content as KnowledgeBaseContent

    const knowledgeBaseData = {
      content: customizations.content || content.content,
      editor: customizations.editor || {
        type: "text",
        content: content.content,
      },
    }

    const created = await this.knowledgeBaseBusinessLogic.create(
      knowledgeBaseData,
      context,
    )
    return created.id
  }

  async incrementDownloadCount(id: string): Promise<void> {
    await this.db.incrementDownloadCount(id)
  }

  async updateRating(id: string, rating: number): Promise<void> {
    if (rating < 0 || rating > 5) {
      throw new Error("Rating must be between 0 and 5")
    }
    await this.db.updateRating(id, rating)
  }

  // Admin functions
  async create(data: LibraryTemplateCreateInput): Promise<LibraryTemplate> {
    return this.db.create(data)
  }

  async update(
    id: string,
    data: LibraryTemplateUpdateInput,
  ): Promise<LibraryTemplate | null> {
    return this.db.update(id, data)
  }

  async delete(id: string): Promise<boolean> {
    return this.db.delete(id)
  }
}
