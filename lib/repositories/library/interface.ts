import { SessionContext } from "../auth/types"

export type LibraryTemplateType =
  | "message_template"
  | "ai_rule"
  | "datasource"
  | "knowledge_base"

export type BusinessCategory =
  | "retail"
  | "healthcare"
  | "finance"
  | "education"
  | "hospitality"
  | "real_estate"
  | "automotive"
  | "technology"
  | "food_beverage"
  | "beauty_wellness"
  | "general"

export interface LibraryTemplate {
  id: string
  title: string
  description: string
  type: LibraryTemplateType
  category: BusinessCategory
  tags: string[]
  content: Record<string, any> // The actual template content (varies by type)
  preview?: string // Optional preview text
  isActive: boolean
  downloadCount: number
  rating: number // 0-5 stars
  createdAt: Date
  updatedAt: Date
  createdBy: string
  version: string
  // Metadata for different template types
  metadata?: {
    variables?: string[] // For message templates
    conditions?: string[] // For AI rules
    actions?: string[] // For AI rules
    dataSourceType?: string // For datasources
    keywords?: string[] // For knowledge base
  }
}

export interface LibraryTemplateCreateInput {
  title: string
  description: string
  type: LibraryTemplateType
  category: BusinessCategory
  tags: string[]
  content: Record<string, any>
  preview?: string
  metadata?: LibraryTemplate["metadata"]
  version?: string
}

export interface LibraryTemplateUpdateInput {
  title?: string
  description?: string
  category?: BusinessCategory
  tags?: string[]
  content?: Record<string, any>
  preview?: string
  isActive?: boolean
  metadata?: LibraryTemplate["metadata"]
  version?: string
}

export interface LibraryTemplateQueryParams {
  search?: string
  type?: LibraryTemplateType
  category?: BusinessCategory
  tags?: string[]
  isActive?: boolean
  page?: number
  limit?: number
  sortBy?: "title" | "downloadCount" | "rating" | "createdAt" | "updatedAt"
  sortOrder?: "ASC" | "DESC"
}

export interface LibraryTemplateInstallInput {
  templateId: string
  customizations?: Record<string, any> // Allow users to customize before installing
}

export interface LibraryTemplateInstallResult {
  success: boolean
  installedId: string
  type: LibraryTemplateType
  message: string
}

export interface LibraryTemplateBusinessLogicInterface {
  // Template management
  getById(id: string): Promise<LibraryTemplate | null>
  getAll(params: LibraryTemplateQueryParams): Promise<{
    items: LibraryTemplate[]
    total: number
    page: number
    limit: number
  }>
  search(
    query: string,
    params?: LibraryTemplateQueryParams,
  ): Promise<{
    items: LibraryTemplate[]
    total: number
  }>

  // Installation
  install(
    input: LibraryTemplateInstallInput,
    context: SessionContext,
  ): Promise<LibraryTemplateInstallResult>

  // Statistics
  incrementDownloadCount(id: string): Promise<void>
  updateRating(id: string, rating: number): Promise<void>

  // Admin functions (for managing the library)
  create(data: LibraryTemplateCreateInput): Promise<LibraryTemplate>
  update(
    id: string,
    data: LibraryTemplateUpdateInput,
  ): Promise<LibraryTemplate | null>
  delete(id: string): Promise<boolean>
}

// Helper types for template content structure
export interface MessageTemplateContent {
  title: string
  query: string
  template: string
  variables?: string[]
}

export interface AiRuleContent {
  name: string
  description?: string
  conditions: string[]
  actions: string[]
}

export interface DatasourceContent {
  name: string
  type: string
  url?: string
  content?: string
  accessKey?: string
}

export interface KnowledgeBaseContent {
  content: string
  keywords: string[]
  category?: string
}
