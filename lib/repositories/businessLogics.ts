import { ConsoleEventSender } from "@/microservices/ConsoleEventSender"
import { ResendEmailSender } from "@/microservices/ResendEmailSender"

import { AiRuleBusinessLogic } from "./aiRules"
import { MongoAiRuleRepository } from "./aiRules/MongoRepository"
import { PineconeAiRuleVectorDBRepository } from "./aiRules/VectorDBRepository"

import { AiWorkflowExecutionBusinessLogic } from "./aiWorkflowExecutions"
import { MongoAiWorkflowExecutionRepository } from "./aiWorkflowExecutions/MongoRepository"

import { AuthBusinessLogic, MongoAuthDBRepository } from "./auth"
import { AuthDBRedisRepository } from "./auth/RedisInMemoryRepository"

import { BroadcastRecipientBusinessLogic } from "./broadcast/BroadcastRecipientBusinessLogic"
import { WhatsAppBroadcastSender } from "./broadcast/BroadcastSender"
import { BroadcastBusinessLogic } from "./broadcast/BusinessLogic"
import { MongoBroadcastRecipientRepository } from "./broadcast/MongoBroadcastRecipientRepository"
import { MongoBroadcastRepository } from "./broadcast/MongoRepository"

import { DeviceBusinessLogicImpl } from "./devices/BusinessLogic"
import { MongoDeviceRepository } from "./devices/MongoRepository"

import { ContactBusinessLogic } from "./contacts"
import { MongoContactRepository } from "./contacts/MongoRepository"

import { ConversationMessageBusinessLogic } from "./conversationMessages"
import { MongoConversationMessageRepository } from "./conversationMessages/MongoRepository"

import { ConversationBusinessLogic } from "./conversations"
import { MongoConversationRepository } from "./conversations/MongoRepository"

import { CustomerProfileBusinessLogic } from "./customerProfiles"
import { MongoCustomerProfileRepository } from "./customerProfiles/MongoRepository"

import { DatasourceBusinessLogic } from "./datasources"
import { MongoDatasourceRepository } from "./datasources/MongoRepository"
import { PineconeDatasourceVectorDBRepository } from "./datasources/VectorDBRepository"

import { KnowledgeBaseBusinessLogic } from "./knowledgeBase/BusinessLogic"
import { PineconeKnowledgeBaseParsedVectorDBRepository } from "./knowledgeBase/KnowledgeBaseParsedVectorDBRepository"
import { KnowledgebaseParserService } from "./knowledgeBase/KnowledgeBaseParserService"
import { MongoKnowledgeBaseParsedRepository } from "./knowledgeBase/MongoParsedRepository"
import { MongoKnowledgeBaseRepository } from "./knowledgeBase/MongoRepository"

import { MessageTemplateBusinessLogic } from "./messageTemplates"
import { MongoMessageTemplateRepository } from "./messageTemplates/MongoRepository"
import { PineconeMessageTemplateVectorDBRepository } from "./messageTemplates/VectorDBRepository"

import { UserBusinessLogic } from "./users"
import { MongoUserRepository } from "./users/MongoRepository"

import { AccountBusinessLogicImpl } from "./account"
import { AccountDBRepository } from "./account/MongoRepository"

import { TestConversationBusinessLogic } from "./testConversations"
import { MongoTestConversationRepository } from "./testConversations/MongoRepository"

import { LibraryTemplateBusinessLogic } from "./library/BusinessLogic"
import { LibraryTemplateDBRepository } from "./library/DBRepository"

import { driver } from "./LiveMongoDriver"
import { driver as inMemoryDriver } from "./LiveRedisDriver"

import { getMessageQueue } from "../queue"

// Cache
let _businessLogics: ReturnType<typeof createBusinessLogics> | null = null

export function getBusinessLogics() {
  if (!_businessLogics) {
    _businessLogics = createBusinessLogics()
  }
  return _businessLogics
}

function createBusinessLogics() {
  driver.connect()
  const authDb = new MongoAuthDBRepository(driver)
  const inMemoryAuthRepo = new AuthDBRedisRepository(inMemoryDriver)
  const emailSender = new ResendEmailSender()
  const eventSender = new ConsoleEventSender()

  const authBusinessLogic = new AuthBusinessLogic(
    authDb,
    inMemoryAuthRepo,
    emailSender,
    eventSender,
  )

  const aiRulesDb = new MongoAiRuleRepository(driver)
  const aiRulesVectordb = new PineconeAiRuleVectorDBRepository(process.env.PINECONE_API_KEY!)
  const aiRulesBusinessLogic = new AiRuleBusinessLogic(aiRulesDb, aiRulesVectordb)

  const workflowExecutionsDb = new MongoAiWorkflowExecutionRepository(driver)
  const workflowExecutionsBusinessLogic = new AiWorkflowExecutionBusinessLogic(workflowExecutionsDb)

  const contactsDb = new MongoContactRepository(driver)
  const contactsBusinessLogic = new ContactBusinessLogic(contactsDb)

  const customerProfilesDb = new MongoCustomerProfileRepository(driver)
  const customerProfilesBusinessLogic = new CustomerProfileBusinessLogic(customerProfilesDb)

  const datasourcesDb = new MongoDatasourceRepository(driver)
  const datasourcesVectordb = new PineconeDatasourceVectorDBRepository(process.env.PINECONE_API_KEY!)
  const datasourcesBusinessLogic = new DatasourceBusinessLogic(datasourcesDb, datasourcesVectordb)

  const messageTemplatesDb = new MongoMessageTemplateRepository(driver)
  const messageTemplatesVectordb = new PineconeMessageTemplateVectorDBRepository(process.env.PINECONE_API_KEY!)
  const messageTemplatesBusinessLogic = new MessageTemplateBusinessLogic(
    messageTemplatesDb,
    messageTemplatesVectordb,
  )

  const conversationMessagesDb = new MongoConversationMessageRepository(driver)
  const conversationMessagesBusinessLogic = new ConversationMessageBusinessLogic(conversationMessagesDb)

  const conversationsDb = new MongoConversationRepository(driver)
  const conversationBusinessLogic = new ConversationBusinessLogic(conversationsDb)

  const testConversationsDb = new MongoTestConversationRepository(driver)
  const testConversationBusinessLogic = new TestConversationBusinessLogic(testConversationsDb)

  const usersDb = new MongoUserRepository(driver)
  const usersBusinessLogic = new UserBusinessLogic(usersDb)

  const accountDb = new AccountDBRepository(usersBusinessLogic)
  const accountBusinessLogic = new AccountBusinessLogicImpl(accountDb)

  const knowledgeBaseDb = new MongoKnowledgeBaseRepository(driver)
  const knowledgeBaseParsedDb = new MongoKnowledgeBaseParsedRepository(driver)
  const knowledgeBaseParsedVectordb = new PineconeKnowledgeBaseParsedVectorDBRepository(process.env.PINECONE_API_KEY!)
  const knowledgeBaseBusinessLogic = new KnowledgeBaseBusinessLogic(
    knowledgeBaseDb,
    knowledgeBaseParsedDb,
    knowledgeBaseParsedVectordb,
    {
      dataSource: datasourcesBusinessLogic,
      aiRule: aiRulesBusinessLogic,
      messageTemplate: messageTemplatesBusinessLogic,
    },
    new KnowledgebaseParserService(),
  )

  const devicesDb = new MongoDeviceRepository(driver)
  const devicesBusinessLogic = new DeviceBusinessLogicImpl(devicesDb)

  const broadcastDb = new MongoBroadcastRepository(driver)
  const broadcastRecipientDb = new MongoBroadcastRecipientRepository(driver)
  const broadcastRecipientBusinessLogic = new BroadcastRecipientBusinessLogic(broadcastRecipientDb, contactsDb)

  const broadcastSender = new WhatsAppBroadcastSender(
    contactsBusinessLogic,
    devicesBusinessLogic,
    broadcastRecipientBusinessLogic,
    getMessageQueue(),
  )

  const broadcastBusinessLogic = new BroadcastBusinessLogic(
    broadcastDb,
    broadcastSender,
    broadcastRecipientBusinessLogic,
  )

  const libraryTemplateDb = new LibraryTemplateDBRepository(driver, process.env.MONGODB_DB_NAME!)
  const libraryTemplateBusinessLogic = new LibraryTemplateBusinessLogic(
    libraryTemplateDb,
    messageTemplatesBusinessLogic,
    aiRulesBusinessLogic,
    datasourcesBusinessLogic,
    knowledgeBaseBusinessLogic,
  )

  return {
    authBusinessLogic,
    aiRulesBusinessLogic,
    workflowExecutionsBusinessLogic,
    contactsBusinessLogic,
    customerProfilesBusinessLogic,
    datasourcesBusinessLogic,
    messageTemplatesBusinessLogic,
    conversationMessagesBusinessLogic,
    conversationBusinessLogic,
    testConversationBusinessLogic,
    usersBusinessLogic,
    accountBusinessLogic,
    knowledgeBaseBusinessLogic,
    devicesBusinessLogic,
    broadcastBusinessLogic,
    broadcastRecipientBusinessLogic,
    libraryTemplateBusinessLogic,
  }
}
