import { BaseDbRepository } from "../BaseDBRepository"
import {
  BroadcastRecipient,
  BroadcastRecipientCreateInput,
  BroadcastRecipientUpdateInput,
  BroadcastRecipientQueryParams,
} from "./interface"

export interface BroadcastRecipientDBRepository
  extends BaseDbRepository<
    BroadcastRecipient,
    BroadcastRecipientCreateInput,
    BroadcastRecipientUpdateInput,
    BroadcastRecipientQueryParams
  > {
  getByBroadcastId(broadcastId: string): Promise<BroadcastRecipient[]>
  deleteByBroadcastId(
    broadcastId: string,
    hardDelete?: boolean,
  ): Promise<number>
}
