import { SessionContext } from "../auth/types"
import {
  BroadcastRecipientBusinessLogicInterface,
  Broadcast,
  BroadcastRecipientStatus,
} from "./interface"
import { ContactBusinessLogicInterface } from "../contacts/interface"
import { DeviceBusinessLogic } from "../devices/interface"
import { createError } from "@/lib/utils/common"
import { MessageQueue, BroadcastSendMessage } from "@/lib/queue/MessageQueue"
import { getCallbackUrl } from "@/lib/config/queue"

export interface BroadcastSendResult {
  sentCount: number
  failedCount: number
}

export interface BroadcastSender {
  sendBroadcast(
    broadcast: Broadcast,
    context: SessionContext,
  ): Promise<BroadcastSendResult>
}

export class WhatsAppBroadcastSender implements BroadcastSender {
  constructor(
    private contactBusinessLogic: ContactBusinessLogicInterface,
    private deviceBusinessLogic: DeviceBusinessLogic,
    private broadcastRecipientBusinessLogic: BroadcastRecipientBusinessLogicInterface,
    private messageQueue: MessageQueue, // ✅ Injected queue service
  ) {}

  async sendBroadcast(
    broadcast: Broadcast,
    context: SessionContext,
  ): Promise<BroadcastSendResult> {
    const device = await this.deviceBusinessLogic.getById(
      broadcast.deviceId,
      context,
    )
    if (!device) throw createError("Device not found", "DEVICE_NOT_FOUND")
    if (!device.isActive)
      throw createError("Device is not active", "DEVICE_INACTIVE")
    if (device.status !== "CONNECTED")
      throw createError("Device is not connected", "DEVICE_NOT_CONNECTED")

    // Get recipients for this broadcast
    const recipients =
      await this.broadcastRecipientBusinessLogic.getAllRecipientsIdsForBroadcast(
        broadcast,
        context,
      )

    let enqueuedCount = 0
    let failedCount = 0

    for (const recipient of recipients) {
      try {
        const contact = await this.contactBusinessLogic.getById(
          recipient,
          context,
        )
        if (!contact) continue

        const broadcastRecipient =
          await this.broadcastRecipientBusinessLogic.create(
            {
              broadcastId: broadcast.id,
              contactId: contact.id,
              name: contact.name,
              phone: contact.phone,
              email: contact.email,
              status: BroadcastRecipientStatus.PENDING,
            },
            context,
          )

        const message: BroadcastSendMessage = {
          type: "broadcast.send",
          payload: {
            broadcastId: broadcast.id,
            broadcastRecipientId: broadcastRecipient.id,
          },
          context: {
            userId: context.user.id,
            organizationId: context.organization?.id,
          },
          callbackUrl: getCallbackUrl(),
          internalSystemToken: process.env.INTERNAL_SECRET_TOKEN!,
        }

        await this.messageQueue.enqueue("broadcast.send", message)
        enqueuedCount++
      } catch (err) {
        console.error(`Failed to enqueue message for contact ${recipient}`, err)
        failedCount++
      }
    }

    return {
      sentCount: enqueuedCount,
      failedCount,
    }
  }
}
