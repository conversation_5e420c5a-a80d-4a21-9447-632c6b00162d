import { Collection, Document } from "mongodb"
import { v4 as uuidv4 } from "uuid"
import { MongoDriver } from "../MongoDriver"
import { BroadcastRecipientDBRepository } from "./BroadcastRecipientDBRepository"
import {
  BroadcastRecipient,
  BroadcastRecipientCreateInput,
  BroadcastRecipientUpdateInput,
  BroadcastRecipientQueryParams,
  BroadcastRecipientStatus,
} from "./interface"
import { buildMongoQuery } from "../queryBuilder"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"

export class MongoBroadcastRecipientRepository
  implements BroadcastRecipientDBRepository
{
  private collection: Collection<Document>

  constructor(private driver: MongoDriver) {
    this.collection = driver.getCollection(
      MONGO_COLLECTIONS.BROADCAST_RECIPIENTS,
    )
    this.ensureIndexes()
  }

  private async ensureIndexes() {
    await this.collection.createIndex({ id: 1 }, { unique: true })
    await this.collection.createIndex({ broadcastId: 1 })
    await this.collection.createIndex({ contactId: 1 })
    await this.collection.createIndex({ status: 1 })
    await this.collection.createIndex({ organizationId: 1 })
    await this.collection.createIndex({ createdBy: 1 })
    await this.collection.createIndex({ broadcastId: 1, contactId: 1 })
  }

  private mapToEntity(doc: Document): BroadcastRecipient {
    return {
      id: doc.id,
      broadcastId: doc.broadcastId,
      contactId: doc.contactId,
      name: doc.name,
      phone: doc.phone,
      email: doc.email,
      status: doc.status,
      sentAt: doc.sentAt,
      deliveredAt: doc.deliveredAt,
      failedAt: doc.failedAt,
      errorMessage: doc.errorMessage,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      organizationId: doc.organizationId,
    }
  }

  async getById(
    id: string,
    includeDeleted = false,
  ): Promise<BroadcastRecipient | null> {
    const filter: any = { id }
    if (!includeDeleted) {
      filter.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(filter)
    return doc ? this.mapToEntity(doc) : null
  }

  async getAll(
    params: BroadcastRecipientQueryParams,
  ): Promise<{ items: BroadcastRecipient[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.offset,
      },
      [],
    )

    const [items, total] = await Promise.all([
      this.collection
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit)
        .toArray(),
      this.collection.countDocuments(query),
    ])

    return {
      items: items.map((doc) => this.mapToEntity(doc)),
      total,
    }
  }

  async getCount(
    params: BroadcastRecipientQueryParams,
  ): Promise<{ total: number }> {
    const { query } = buildMongoQuery(
      { query: {}, sort: {} },
      {
        ...params,
        offset: params?.offset,
      },
      [],
    )

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async getByBroadcastId(broadcastId: string): Promise<BroadcastRecipient[]> {
    const docs = await this.collection
      .find({ broadcastId, deletedAt: { $exists: false } })
      .toArray()
    return docs.map((doc) => this.mapToEntity(doc))
  }

  async create(
    data: BroadcastRecipientCreateInput,
  ): Promise<BroadcastRecipient> {
    const now = new Date()
    const doc = {
      ...data,
      id: uuidv4(),
      status: data.status || BroadcastRecipientStatus.PENDING,
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(doc)
    return this.mapToEntity(doc)
  }

  async bulkCreate(
    data: BroadcastRecipientCreateInput[],
  ): Promise<BroadcastRecipient[]> {
    const now = new Date()
    const docs = data.map((item) => ({
      ...item,
      id: uuidv4(),
      status: item.status || BroadcastRecipientStatus.PENDING,
      createdAt: now,
      updatedAt: now,
    }))

    await this.collection.insertMany(docs)
    return docs.map((doc) => this.mapToEntity(doc))
  }

  async update(
    id: string,
    data: BroadcastRecipientUpdateInput,
  ): Promise<BroadcastRecipient | null> {
    const updateDoc = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateDoc },
      { returnDocument: "after" },
    )

    return result ? this.mapToEntity(result) : null
  }

  async bulkUpdate(
    updates: { id: string; data: BroadcastRecipientUpdateInput }[],
  ): Promise<number> {
    const operations = updates.map(({ id, data }) => ({
      updateOne: {
        filter: { id, deletedAt: { $exists: false } },
        update: { $set: { ...data, updatedAt: new Date() } },
      },
    }))

    const result = await this.collection.bulkWrite(operations)
    return result.modifiedCount
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { id, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount > 0
    }
  }

  async deleteByBroadcastId(
    broadcastId: string,
    hardDelete = false,
  ): Promise<number> {
    if (hardDelete) {
      const result = await this.collection.deleteMany({ broadcastId })
      return result.deletedCount
    } else {
      const result = await this.collection.updateMany(
        { broadcastId, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } },
      )
      return result.modifiedCount
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id, deletedAt: { $exists: true } },
      { $unset: { deletedAt: "" } },
    )
    return result.modifiedCount > 0
  }

  async clear(): Promise<void> {}
}
