import {
  BroadcastRecipientBusinessLogicInterface,
  BroadcastRecipient,
  BroadcastRecipientCreateInput,
  BroadcastRecipientUpdateInput,
  BroadcastRecipientQueryParams,
  Broadcast,
} from "./interface"
import { SessionContext } from "../auth/types"
import { createError } from "@/lib/utils/common"
import { BroadcastRecipientDBRepository } from "./BroadcastRecipientDBRepository"
import { Contact, ContactDBRepository } from "../contacts"
import { IN, LogicalFilter, NOT_IN } from "../BaseDBRepository"

export class BroadcastRecipientBusinessLogic
  implements BroadcastRecipientBusinessLogicInterface
{
  constructor(
    private db: BroadcastRecipientDBRepository,
    private contactDb: ContactDBRepository,
  ) {}
  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim() === "") {
      throw createError("Invalid ID provided", "INVALID_ID")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    } else {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    return filters
  }

  private trimCreateInput(
    data: BroadcastRecipientCreateInput,
  ): BroadcastRecipientCreateInput {
    if (
      !data.broadcastId ||
      typeof data.broadcastId !== "string" ||
      data.broadcastId.trim() === ""
    ) {
      throw createError("Broadcast ID is required", "INVALID_BROADCAST_ID")
    }

    if (
      !data.contactId ||
      typeof data.contactId !== "string" ||
      data.contactId.trim() === ""
    ) {
      throw createError("Contact ID is required", "INVALID_CONTACT_ID")
    }

    if (
      !data.name ||
      typeof data.name !== "string" ||
      data.name.trim() === ""
    ) {
      throw createError("Name is required", "INVALID_NAME")
    }

    return {
      ...data,
      broadcastId: data.broadcastId.trim(),
      contactId: data.contactId.trim(),
      name: data.name.trim(),
    }
  }

  async create(
    data: BroadcastRecipientCreateInput,
    context: SessionContext,
  ): Promise<BroadcastRecipient> {
    const trimmedData = this.trimCreateInput(data)

    const dataWithContext = {
      ...trimmedData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    return this.db.create(dataWithContext)
  }

  async getById(
    id: string,
    context: SessionContext,
  ): Promise<BroadcastRecipient | null> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const filters = [{ field: "id", value: id }, ...contextFilters]

    const result = await this.db.getAll({ filters, limit: 1 })
    return result.items.length > 0 ? result.items[0] : null
  }

  async getAll(
    params: BroadcastRecipientQueryParams,
    context: SessionContext,
  ): Promise<{ items: BroadcastRecipient[]; total: number }> {
    const contextFilters = this.buildContextFilters(context)

    const queryParams = {
      ...params,
      filters: [...(params.filters || []), ...contextFilters],
    }

    return this.db.getAll(queryParams)
  }

  async getByBroadcastId(
    broadcastId: string,
    context: SessionContext,
  ): Promise<BroadcastRecipient[]> {
    this.validateId(broadcastId)

    const contextFilters = this.buildContextFilters(context)
    const filters = [
      { field: "broadcastId", value: broadcastId },
      ...contextFilters,
    ]

    const result = await this.db.getAll({ filters })
    return result.items
  }

  async update(
    id: string,
    data: BroadcastRecipientUpdateInput,
    context: SessionContext,
  ): Promise<BroadcastRecipient | null> {
    this.validateId(id)

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA")
    }

    // Check if recipient exists and belongs to the current context
    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Broadcast recipient not found", "NOT_FOUND")
    }

    const updateData = {
      ...data,
      updatedBy: context.user.id,
    }

    return this.db.update(id, updateData)
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [{ field: "id", value: id }, ...contextFilters],
    })

    if (existingResult.items.length === 0) {
      throw createError("Broadcast recipient not found", "NOT_FOUND")
    }

    return this.db.delete(id, hardDelete)
  }

  async deleteByBroadcastId(
    broadcastId: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<number> {
    this.validateId(broadcastId)

    const contextFilters = this.buildContextFilters(context)
    const existingResult = await this.db.getAll({
      filters: [
        { field: "broadcastId", value: broadcastId },
        ...contextFilters,
      ],
    })

    if (existingResult.items.length === 0) {
      return 0 // No recipients found for this broadcast
    }

    return this.db.deleteByBroadcastId(broadcastId, hardDelete)
  }

  private buildFilterQuery(
    broadcast: {
      recipientTags?: string[]
      excludedRecipientIds?: string[]
      manualSelectedTargetRecipients?: string[]
    },
    context: SessionContext,
  ): LogicalFilter<Contact> | null {
    const tags = broadcast.recipientTags || []
    const excludedIds = broadcast.excludedRecipientIds || []
    const includedIds = broadcast.manualSelectedTargetRecipients || []

    const contextFilters: LogicalFilter<Contact>[] =
      this.buildContextFilters(context)

    const filterEnhanced: LogicalFilter<Contact> = {
      OR: [],
    }

    // 1. If no tags selected, get all (within context), excluding excludedIds
    if (tags.length === 0) {
      const filter: LogicalFilter<Contact> = {
        AND: [
          ...contextFilters,
          ...(excludedIds.length > 0 ? [NOT_IN("id", excludedIds)] : []),
        ],
      }
      filterEnhanced.OR!.push(filter)
    }

    // 2. If tags selected, include only contacts with those tags
    if (tags.length > 0) {
      const filter: LogicalFilter<Contact> = {
        AND: [
          ...contextFilters,
          IN("tags", tags),
          ...(excludedIds.length > 0 ? [NOT_IN("id", excludedIds)] : []),
        ],
      }
      filterEnhanced.OR!.push(filter)
    }

    // 3. Manually included contacts override exclusion
    if (includedIds.length > 0) {
      filterEnhanced.OR!.push(IN("id", includedIds))
    }

    // 4. If no recipients are possible, short-circuit to 0
    if (filterEnhanced.OR!.length === 0) {
      return null
    }

    return filterEnhanced
  }

  async countEstimatedTotalRecipientForBroadcastData(
    broadcast: {
      recipientTags?: string[]
      excludedRecipientIds?: string[]
      manualSelectedTargetRecipients?: string[]
    },
    context: SessionContext,
  ): Promise<number> {
    const filterEnhanced = this.buildFilterQuery(broadcast, context)
    if (!filterEnhanced) {
      return 0
    }

    // Use the enhanced filter query
    const countResult = await this.contactDb.getCount({
      filterEnhanced,
    })

    return countResult.total
  }

  async getAllRecipientsIdsForBroadcast(
    broadcast: Broadcast,
    context: SessionContext,
  ): Promise<string[]> {
    const filterEnhanced = this.buildFilterQuery(broadcast, context)
    if (!filterEnhanced) {
      return []
    }

    const result = await this.contactDb.getAll({
      filters: [],
      filterEnhanced,
      limit: 1000,
      offset: 1,
    })

    return result.items.map((contact) => contact.id)
  }
}
