"use client"

import { useLocalization } from "@/localization/functions/client"
import { AuthAPI } from "@/lib/services/authApi"
import { useState, useEffect } from "react"
import { authLocales } from "../locales"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useSearchParams } from "next/navigation"
import { CheckCircle, Loader2 } from "lucide-react"

export default function EmailVerificationPage() {
  const searchParams = useSearchParams()
  const paramEmail = searchParams.get("email") || ""

  const { t } = useLocalization("auth", authLocales)
  const [message, setMessage] = useState("")
  const [email, setEmail] = useState(paramEmail)
  const [success, setSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    setEmail(paramEmail)
  }, [paramEmail])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      await AuthAPI.ResendVerification({ email }).request()
      setMessage(t("auth.verification_sent"))
      setSuccess(true)
    } catch (err: any) {
      setMessage(err.message || t("auth.verification_error"))
      setSuccess(false)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-md mx-auto mt-20 mb-20">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">
            {t("auth.email_verification")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!success ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t("auth.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("auth.email")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {t("auth.sending")}
                  </>
                ) : (
                  t("auth.send")
                )}
              </Button>
              {message && (
                <p className="text-sm text-muted-foreground">{message}</p>
              )}
            </form>
          ) : (
            <div className="flex flex-col items-center justify-center py-10">
              <CheckCircle className="text-green-500 w-16 h-16 mb-4" />
              <p className="text-lg font-medium text-green-600">{message}</p>
              <p className="text-sm text-muted-foreground mt-2 text-center">
                {t("auth.check_email")}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
