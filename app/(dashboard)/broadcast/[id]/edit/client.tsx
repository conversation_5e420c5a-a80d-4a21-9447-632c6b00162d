"use client"

import { useState, useEffect } from "react"
import { BroadcastAPI } from "@/lib/services"
import {
  Broadcast,
  BroadcastStatus,
} from "@/lib/repositories/broadcast/interface"
import TabbedBroadcastForm from "@/components/broadcast/TabbedBroadcastForm"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../../locales"

export function EditBroadcastPageClient({
  broadcastId,
}: {
  broadcastId: string
}) {
  const { t } = useLocalization("broadcast", locales)
  const [broadcast, setBroadcast] = useState<Broadcast | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loading, setLoading] = useState(true)
  // Load broadcast data
  const fetchBroadcast = async () => {
    try {
      setLoading(true)
      const broadcastResponse = await BroadcastAPI.Detail(broadcastId).request()

      setBroadcast(broadcastResponse)
    } catch (error) {
      console.error(t("edit.errors.fetch_failed"), error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (broadcastId) {
      fetchBroadcast()
    }
  }, [broadcastId])

  const handleSave = async (data: {
    title: string
    message: string
    deviceId: string
    recipientTags?: string[]
    excludedRecipientIds?: string[]
    manualSelectedTargetRecipients?: string[]
  }) => {
    setIsSubmitting(true)
    try {
      await BroadcastAPI.Update(broadcastId, {
        title: data.title,
        message: data.message,
        deviceId: data.deviceId,
        recipientTags: data.recipientTags,
        excludedRecipientIds: data.excludedRecipientIds,
        manualSelectedTargetRecipients: data.manualSelectedTargetRecipients,
      }).request()

      window.location.href = `/broadcast/${broadcastId}`
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    window.location.href = `/broadcast/${broadcastId}`
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  if (!broadcast) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-gray-500">{t("edit.not_found")}</p>
          <button
            onClick={() => {
              window.location.href = `/broadcast`
            }}
            className="mt-4"
          >
            {t("edit.back_to_broadcasts")}
          </button>
        </div>
      </div>
    )
  }

  if (broadcast.status !== BroadcastStatus.DRAFT) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-gray-500">{t("edit.cannot_edit")}</p>
          <button
            onClick={() => {
              window.location.href = `/broadcast/${broadcastId}`
            }}
            className="mt-4"
          >
            {t("edit.view_broadcast")}
          </button>
        </div>
      </div>
    )
  }

  return (
    <TabbedBroadcastForm
      initialBroadcast={broadcast}
      onSave={handleSave}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
      submitButtonText={t("form.edit.submit")}
      title={t("form.edit.title")}
      description={t("form.edit.description")}
    />
  )
}
