"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { BusinessConfig } from "@/lib/config/business-config"
import { SupportAction, SupportConfig } from "@/lib/config/support-config"
import { useLocalization } from "@/localization/functions/client"
import {
  BookOpen,
  Bug,
  ChevronDown,
  Clock,
  CreditCard,
  Lightbulb,
  Mail,
  MessageCircle,
  Phone,
  Search,
  Settings,
  Users,
} from "lucide-react"
import { useRouter } from "next/navigation"
import * as React from "react"
import { useEffect } from "react"
import { supportLocales } from "../locales"

const iconMap = {
  Mail,
  MessageCircle,
  Phone,
  BookOpen,
  CreditCard,
  Bug,
  Lightbulb,
  Settings,
}

interface SupportPageClientProps {
  businessConfig: BusinessConfig
  supportConfig: SupportConfig
}

export default function SupportPageClient({
  businessConfig,
  supportConfig,
}: SupportPageClientProps) {
  const { t, locale } = useLocalization("support", supportLocales)
  const [searchTerm, setSearchTerm] = React.useState("")
  const [selectedCategory, setSelectedCategory] = React.useState("all")
  const router = useRouter()

  useEffect(() => {
    router.refresh()
  }, [locale])

  const filteredFAQ = supportConfig.faq.filter((item) => {
    const matchesSearch =
      item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory =
      selectedCategory === "all" ||
      item.category.toLowerCase().replace(" ", "_") === selectedCategory

    return matchesSearch && matchesCategory
  })

  const categories = Array.from(
    new Set(supportConfig.faq.map((item) => item.category)),
  )

  const handleContactAction = (action: SupportAction) => {
    switch (action.type) {
      case "email":
        const subject = action.subject
          ? `?subject=${encodeURIComponent(action.subject)}`
          : ""
        window.open(`mailto:${action.target}${subject}`)
        break
      case "phone":
        window.open(`tel:${action.target}`)
        break
      case "link":
        window.open(action.target, "_blank")
        break
      default:
        console.warn("Unknown action type:", action.type)
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-8 p-2 h-full overflow-y-auto">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">
          {t("page.title", { businessName: businessConfig.businessInfo.name })}
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t("page.description", {
            businessName: businessConfig.businessInfo.name,
          })}
        </p>
        {businessConfig.businessInfo.tagline && (
          <p className="text-lg text-primary font-medium">
            {businessConfig.businessInfo.tagline}
          </p>
        )}
      </div>

      {/* Support Channels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            {t("channels.title")}
          </CardTitle>
          <p className="text-muted-foreground">{t("channels.subtitle")}</p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {supportConfig.supportChannels.map((channel) => {
              const IconComponent =
                iconMap[channel.icon as keyof typeof iconMap]
              return (
                <Card
                  key={channel.id}
                  className={`cursor-pointer transition-all hover:shadow-md border-l-4 border-l-${channel.color}-500`}
                  onClick={() => handleContactAction(channel.action)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg bg-${channel.color}-100`}>
                        {IconComponent && (
                          <IconComponent
                            className={`h-5 w-5 text-${channel.color}-600`}
                          />
                        )}
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-sm">
                            {channel.name}
                          </h3>
                          {channel.recommended && (
                            <Badge variant="secondary" className="text-xs">
                              {t("channels.recommended_badge")}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {channel.description}
                        </p>
                        <div className="space-y-1">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {t("channels.response_time", {
                              time: channel.responseTime,
                            })}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Users className="h-3 w-3" />
                            {t("channels.availability", {
                              hours: channel.availability,
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>{t("quick_actions.title")}</CardTitle>
          <p className="text-muted-foreground">{t("quick_actions.subtitle")}</p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {supportConfig.quickActions.map((action) => {
              const IconComponent = iconMap[action.icon as keyof typeof iconMap]
              return (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => handleContactAction(action.action)}
                >
                  <CardContent className="p-4 text-center space-y-3">
                    <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      {IconComponent && (
                        <IconComponent className="h-6 w-6 text-primary" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-sm">{action.title}</h3>
                      <p className="text-xs text-muted-foreground mt-1">
                        {action.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Business Hours */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t("business_hours.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">
                  {t("business_hours.weekdays", {
                    start: businessConfig.businessHours.weekdays.start,
                    end: businessConfig.businessHours.weekdays.end,
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">
                  {t("business_hours.weekends", {
                    start: businessConfig.businessHours.weekends.start,
                    end: businessConfig.businessHours.weekends.end,
                  })}
                </span>
              </div>
              <Separator />
              <div className="text-xs text-muted-foreground">
                {t("business_hours.timezone", {
                  timezone: businessConfig.businessHours.timezone,
                })}
              </div>
              <div className="text-xs text-green-600 font-medium">
                {t("business_hours.note")}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact */}
        {supportConfig.emergencyContact.available && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-700">
                {t("emergency.title")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-red-600 mb-4">
                {t("emergency.description")}
              </p>
              <Button
                variant="destructive"
                onClick={() =>
                  handleContactAction({
                    type: "link",
                    target: supportConfig.emergencyContact.contact.whatsapp,
                  })
                }
                className="w-full"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {t("emergency.contact_button")}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>{t("faq.title")}</CardTitle>
          <p className="text-muted-foreground">{t("faq.subtitle")}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t("faq.search_placeholder")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("faq.categories.all")}</SelectItem>
                {categories.map((category) => (
                  <SelectItem
                    key={category}
                    value={category.toLowerCase().replace(" ", "_")}
                  >
                    {t(
                      `faq.categories.${category.toLowerCase().replace(" ", "_")}`,
                    ) || category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* FAQ Items */}
          <div className="space-y-2">
            {filteredFAQ.length > 0 ? (
              filteredFAQ.map((item) => (
                <Collapsible key={item.id}>
                  <CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg border p-4 text-left hover:bg-muted/50">
                    <span className="font-medium">{item.question}</span>
                    <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="px-4 pb-4 pt-2">
                    <p className="text-muted-foreground">{item.answer}</p>
                  </CollapsibleContent>
                </Collapsible>
              ))
            ) : (
              <p className="text-center text-muted-foreground py-8">
                {t("faq.no_results")}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Support Tips */}
      <Card>
        <CardHeader>
          <CardTitle>{t("tips.title")}</CardTitle>
          <p className="text-muted-foreground">{t("tips.subtitle")}</p>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {supportConfig.tips.map((tip, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0" />
                <span className="text-sm">{tip}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Footer Contact */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold">
              {t("footer.still_need_help")}
            </h3>
            <p className="text-muted-foreground">
              {t("footer.description", {
                businessName: businessConfig.businessInfo.name,
              })}
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() =>
                  handleContactAction({
                    type: "email",
                    target: businessConfig.contact.email,
                  })
                }
                className="gap-2"
              >
                <Mail className="h-4 w-4" />
                {t("actions.send_email")}
              </Button>
              <Button
                variant="outline"
                onClick={() =>
                  handleContactAction({
                    type: "link",
                    target: businessConfig.contact.whatsappLink,
                  })
                }
                className="gap-2"
              >
                <MessageCircle className="h-4 w-4" />
                {t("actions.chat_whatsapp")}
              </Button>
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>{businessConfig.contact.email}</div>
              <div>{businessConfig.contact.whatsapp}</div>
              <div>{businessConfig.contact.address}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
