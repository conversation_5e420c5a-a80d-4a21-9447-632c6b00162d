import {
  loadBusinessConfig,
  BusinessConfig,
} from "@/lib/config/business-config"
import { SupportConfig } from "@/lib/config/support-config"

/**
 * Replace template variables in support config with business config values
 */
function replaceSupportConfigTemplates(
  supportConfig: SupportConfig,
  businessConfig: BusinessConfig,
): SupportConfig {
  const configStr = JSON.stringify(supportConfig)
  const replacedStr = configStr
    .replace(/\{\{businessEmail\}\}/g, businessConfig.contact.email)
    .replace(/\{\{businessPhone\}\}/g, businessConfig.contact.phone)
    .replace(
      /\{\{businessWhatsappLink\}\}/g,
      businessConfig.contact.whatsappLink,
    )
    .replace(/\{\{businessWebsite\}\}/g, businessConfig.contact.website)

  return JSON.parse(replacedStr) as SupportConfig
}

/**
 * Load both business and support configurations on the server side
 */
export async function loadSupportPageConfigs(locale: string): Promise<{
  businessConfig: BusinessConfig
  supportConfig: SupportConfig
}> {
  try {
    // Load business config from server-side
    const businessConfig = await loadBusinessConfig(locale)

    // Load support config - this needs to be done server-side too
    let rawSupportConfig: SupportConfig

    try {
      // Try to load the specific locale support config
      const supportConfigModule = await import(
        `../config/support-config-${locale}.json`
      )
      rawSupportConfig = supportConfigModule.default as SupportConfig
    } catch (error) {
      console.log(
        `Support config not found for locale ${locale}, falling back to English`,
      )
      // Fallback to English config
      const supportConfigModule = await import(
        `../config/support-config-en.json`
      )
      rawSupportConfig = supportConfigModule.default as SupportConfig
    }

    // Replace template variables in support config with business config values
    const processedSupportConfig = replaceSupportConfigTemplates(
      rawSupportConfig,
      businessConfig,
    )

    return {
      businessConfig,
      supportConfig: processedSupportConfig,
    }
  } catch (error) {
    console.error("Failed to load support page configurations:", error)
    throw error
  }
}
