"use client"

import { useState, useEffect } from "react"
import { useRouter, usePara<PERSON> } from "next/navigation"
import { useLocalization } from "@/localization/functions/client"
import { libraryLocales } from "../locales"
import { LibraryAPI } from "@/lib/services/libraryAPI"
import { LibraryTemplate } from "@/lib/repositories/library/interface"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  ArrowLeft,
  Download,
  Star,
  Calendar,
  User,
  LayoutTemplate,
  Brain,
  Database,
  Book,
  Copy,
  Check,
} from "lucide-react"
import { toast } from "sonner"

const TEMPLATE_TYPE_ICONS = {
  message_template: LayoutTemplate,
  ai_rule: Brain,
  datasource: Database,
  knowledge_base: Book,
}

export default function LibraryTemplateDetailPage() {
  const router = useRouter()
  const params = useParams()
  const { t } = useLocalization("library", libraryLocales)
  const [template, setTemplate] = useState<LibraryTemplate | null>(null)
  const [loading, setLoading] = useState(true)
  const [installing, setInstalling] = useState(false)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    if (params.id) {
      loadTemplate(params.id as string)
    }
  }, [params.id])

  const loadTemplate = async (id: string) => {
    try {
      setLoading(true)
      const response = await LibraryAPI.GetById(id).request()
      setTemplate(response)
    } catch (error) {
      console.error("Error loading template:", error)
      toast.error(t("messages.load_detail_failed"))
      router.push("/library")
    } finally {
      setLoading(false)
    }
  }

  const handleInstall = async () => {
    if (!template) return

    try {
      setInstalling(true)
      const response = await LibraryAPI.Install({
        templateId: template.id,
      }).request()

      if (response.success) {
        toast.success(t("messages.install_success"))
        // Navigate to the appropriate page based on template type
        switch (response.type) {
          case "message_template":
            router.push("/message-templates")
            break
          case "ai_rule":
            router.push("/ai-rules")
            break
          case "datasource":
            router.push("/datasources")
            break
          case "knowledge_base":
            router.push("/knowledge-base")
            break
        }
      } else {
        toast.error(response.message)
      }
    } catch (error) {
      console.error("Error installing template:", error)
      toast.error(t("messages.install_failed"))
    } finally {
      setInstalling(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      toast.success(t("detail.copied"))
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error(t("detail.copy_failed"))
    }
  }

  const renderContentPreview = () => {
    if (!template) return null

    switch (template.type) {
      case "message_template":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">
                {t("content_preview.template_content")}
              </h4>
              <div className="bg-gray-50 p-4 rounded-lg relative">
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() =>
                    copyToClipboard(template.content.template || "")
                  }
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <pre className="text-sm whitespace-pre-wrap pr-8">
                  {template.content.template ||
                    t("content_preview.no_template_content")}
                </pre>
              </div>
            </div>
            {template.content.variables &&
              template.content.variables.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">
                    {t("content_preview.variables")}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {template.content.variables.map((variable: string) => (
                      <Badge key={variable} variant="outline">
                        {`{${variable}}`}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
          </div>
        )

      case "ai_rule":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">
                {t("content_preview.conditions")}
              </h4>
              <ul className="space-y-1">
                {(template.content.conditions || []).map(
                  (condition: string, index: number) => (
                    <li key={index} className="text-sm bg-blue-50 p-2 rounded">
                      {condition}
                    </li>
                  ),
                )}
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">
                {t("content_preview.actions")}
              </h4>
              <ul className="space-y-1">
                {(template.content.actions || []).map(
                  (action: string, index: number) => (
                    <li key={index} className="text-sm bg-green-50 p-2 rounded">
                      {action}
                    </li>
                  ),
                )}
              </ul>
            </div>
          </div>
        )

      case "datasource":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">
                {t("content_preview.data_source_config")}
              </h4>
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div>
                  <strong>{t("content_preview.type")}:</strong>{" "}
                  {template.content.type}
                </div>
                {template.content.url && (
                  <div>
                    <strong>{t("content_preview.url")}:</strong>{" "}
                    {template.content.url}
                  </div>
                )}
                {template.content.content && (
                  <div>
                    <strong>{t("content_preview.content")}:</strong>
                    <pre className="mt-1 text-sm whitespace-pre-wrap">
                      {template.content.content}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        )

      case "knowledge_base":
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">
                {t("content_preview.knowledge_base_content")}
              </h4>
              <div className="bg-gray-50 p-4 rounded-lg relative">
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() =>
                    copyToClipboard(template.content.content || "")
                  }
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <div className="text-sm whitespace-pre-wrap pr-8">
                  {template.content.content || t("content_preview.no_content")}
                </div>
              </div>
            </div>
            {template.content.keywords &&
              template.content.keywords.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">
                    {t("content_preview.keywords")}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {template.content.keywords.map((keyword: string) => (
                      <Badge key={keyword} variant="outline">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
          </div>
        )

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            {t("content_preview.preview_not_available")}
          </div>
        )
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!template) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">{t("detail.template_not_found")}</p>
        <Button
          variant="outline"
          onClick={() => router.push("/library")}
          className="mt-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("detail.back_to_library")}
        </Button>
      </div>
    )
  }

  const IconComponent = TEMPLATE_TYPE_ICONS[template.type]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push("/library")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("detail.back_to_library")}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Template Info */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <IconComponent className="h-8 w-8 text-blue-600" />
                  <div>
                    <CardTitle className="text-2xl">{template.title}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">
                        {t(`template_types.${template.type}`)}
                      </Badge>
                      <Badge variant="outline">
                        {t(`business_categories.${template.category}`)}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">
                    {template.rating.toFixed(1)}
                  </span>
                </div>
              </div>
              <CardDescription className="text-base">
                {template.description}
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Content Preview */}
          <Card>
            <CardHeader>
              <CardTitle>{t("detail.preview")}</CardTitle>
            </CardHeader>
            <CardContent>{renderContentPreview()}</CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Install Card */}
          <Card>
            <CardHeader>
              <CardTitle>{t("detail.install_section_title")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={handleInstall}
                disabled={installing}
                className="w-full"
                size="lg"
              >
                {installing
                  ? t("detail.installing")
                  : t("detail.install_template")}
              </Button>

              <div className="text-sm text-gray-600 space-y-2">
                <div className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  <span>
                    {template.downloadCount} {t("card.downloads")}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {t("detail.updated", {
                      date: new Date(template.updatedAt).toLocaleDateString(),
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>
                    {t("detail.version", { version: template.version })}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          {template.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t("detail.tags_section_title")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {template.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
