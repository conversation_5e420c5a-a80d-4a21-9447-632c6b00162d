"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useLocalization } from "@/localization/functions/client"
import { libraryLocales } from "./locales"
import { LibraryAPI } from "@/lib/services/libraryAPI"
import {
  LibraryTemplate,
  LibraryTemplateType,
  BusinessCategory,
} from "@/lib/repositories/library/interface"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Search,
  Download,
  Star,
  LayoutTemplate,
  Brain,
  Database,
  Book,
  Grid3X3,
  List,
} from "lucide-react"
import { toast } from "sonner"

const TEMPLATE_TYPE_ICONS = {
  message_template: LayoutTemplate,
  ai_rule: Brain,
  datasource: Database,
  knowledge_base: Book,
}

export default function LibraryPage() {
  const router = useRouter()
  const { t } = useLocalization("library", libraryLocales)
  const [templates, setTemplates] = useState<LibraryTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedType, setSelectedType] = useState<LibraryTemplateType | "all">(
    "all",
  )
  const [selectedCategory, setSelectedCategory] = useState<
    BusinessCategory | "all"
  >("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const [installing, setInstalling] = useState<string[]>([])

  const loadTemplates = async (reset = false) => {
    try {
      setLoading(true)
      const currentPage = reset ? 1 : page

      const params = {
        search: searchQuery || undefined,
        type: selectedType !== "all" ? selectedType : undefined,
        category: selectedCategory !== "all" ? selectedCategory : undefined,
        page: currentPage,
        limit: 20,
        sortBy: "downloadCount" as const,
        sortOrder: "DESC" as const,
      }

      const response = await LibraryAPI.GetAll(params).request()

      if (reset) {
        setTemplates(response.items)
        setPage(1)
      } else {
        setTemplates((prev) => [...prev, ...response.items])
      }

      setTotal(response.total)
      setHasMore(response.items.length === 20)
    } catch (error) {
      console.error("Error loading templates:", error)
      toast.error(t("messages.load_failed"))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadTemplates(true)
  }, [searchQuery, selectedType, selectedCategory])

  const handleSearch = (value: string) => {
    setSearchQuery(value)
  }

  const handleInstall = async (template: LibraryTemplate) => {
    try {
      setInstalling((prev) => [...prev, template.id])
      const response = await LibraryAPI.Install({
        templateId: template.id,
      }).request()

      if (response.success) {
        toast.success(t("messages.install_success"))
        // Navigate to the appropriate page based on template type
        switch (response.type) {
          case "message_template":
            router.push("/message-templates")
            break
          case "ai_rule":
            router.push("/ai-rules")
            break
          case "datasource":
            router.push("/datasources")
            break
          case "knowledge_base":
            router.push("/knowledge-base")
            break
        }
      } else {
        toast.error(response.message)
      }
    } catch (error) {
      console.error("Error installing template:", error)
      toast.error(t("messages.install_failed"))
    } finally {
      setInstalling((prev) => prev.filter((id) => id !== template.id))
    }
  }

  const handleViewDetails = (template: LibraryTemplate) => {
    router.push(`/library/${template.id}`)
  }

  const loadMore = () => {
    setPage((prev) => prev + 1)
    loadTemplates(false)
  }

  const renderTemplateCard = (template: LibraryTemplate) => {
    const IconComponent = TEMPLATE_TYPE_ICONS[template.type]

    return (
      <Card
        key={template.id}
        className="h-full hover:shadow-md transition-shadow"
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <IconComponent className="h-5 w-5 text-blue-600" />
              <Badge variant="secondary" className="text-xs">
                {t(`template_types.${template.type}`)}
              </Badge>
            </div>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>{template.rating.toFixed(1)}</span>
            </div>
          </div>
          <CardTitle className="text-lg line-clamp-2">
            {template.title}
          </CardTitle>
          <CardDescription className="line-clamp-2">
            {template.description}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{t(`business_categories.${template.category}`)}</span>
              <div className="flex items-center gap-1">
                <Download className="h-4 w-4" />
                <span>{template.downloadCount} {t("card.downloads")}</span>
              </div>
            </div>

            {template.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {template.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {template.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{template.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewDetails(template)}
                className="flex-1"
              >
                {t("card.view_details")}
              </Button>
              <Button
                size="sm"
                onClick={() => handleInstall(template)}
                className="flex-1"
                disabled={installing.includes(template.id)}
              >
                {installing.includes(template.id) ? (
                  <span>{t("card.installing")}</span>
                ) : (
                  <span>{t("card.install")}</span>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-3xl font-bold">{t("page.title")}</h1>
          <p className="text-gray-600">
            {t("page.subtitle")}
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t("page.search_placeholder")}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Select
              value={selectedType}
              onValueChange={(value) => setSelectedType(value as any)}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder={t("filters.all_types")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("filters.all_types")}</SelectItem>
                {(["message_template", "ai_rule", "datasource", "knowledge_base"] as const).map((type) => (
                  <SelectItem key={type} value={type}>
                    {t(`template_types.${type}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedCategory}
              onValueChange={(value) => setSelectedCategory(value as any)}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder={t("filters.all_categories")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("filters.all_categories")}</SelectItem>
                {(["retail", "healthcare", "finance", "education", "hospitality", "real_estate", "automotive", "technology", "food_beverage", "beauty_wellness", "general"] as const).map((category) => (
                  <SelectItem key={category} value={category}>
                    {t(`business_categories.${category}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex border rounded-md">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-4">
        {loading && templates.length === 0 ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">{t("page.loading")}</p>
          </div>
        ) : templates.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600">
              {t("page.no_results")}
            </p>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                {t("page.showing_results", { count: templates.length.toString(), total: total.toString() })}
              </p>
            </div>

            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                  : "space-y-4"
              }
            >
              {templates.map(renderTemplateCard)}
            </div>

            {hasMore && (
              <div className="text-center pt-6">
                <Button variant="outline" onClick={loadMore} disabled={loading}>
                  {loading ? t("page.loading") : t("page.load_more")}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
