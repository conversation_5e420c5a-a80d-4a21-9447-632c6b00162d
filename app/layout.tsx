import type { Metadata } from "next"
import { cookies } from "next/headers"
import { Suspense } from "react"
import "./globals.scss"

import { LoadingIndicator } from "@/components/loading-indicator"
import { Toaster as ToasterSonner } from "@/components/ui/sonner"
import { Toaster } from "@/components/ui/toaster"
import { initializeLogging } from "@/lib/logging/setup"
import { LocalizationProvider } from "@/localization/functions/localization-context"
import { ClientConfigLoader } from "./client_config"
import { kDEFAULT_LANG } from "./constant"

export const metadata: Metadata = {
  title: "CS CRM",
  description: "A CRM with AI capabilities",
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {

  initializeLogging()

  const cookieStore = await cookies()
  const localeFromCookie = cookieStore.get("locale")?.value || kDEFAULT_LANG

  return (
    <html lang={localeFromCookie}>
      <body>
        <Suspense fallback={<LoadingIndicator />}>
          <ClientConfigLoader>
            <LocalizationProvider initialLocale={localeFromCookie}>
              {children}
            </LocalizationProvider>
          </ClientConfigLoader>
          <Toaster />
          <ToasterSonner richColors />
        </Suspense>
      </body>
    </html>
  )
}
