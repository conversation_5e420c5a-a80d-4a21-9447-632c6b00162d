import { ReactNode } from 'react'
import { ClientConfigProvider } from './client'

export async function ClientConfigLoader({
    children,
}: {
    children: ReactNode
}) {
    const config = {
        pusherKey: process.env.PUSHER_KEY!,
        pusherCluster: process.env.PUSHER_CLUSTER!
    }

    if (!config.pusherKey || !config.pusherCluster) {
        throw new Error('Missing config keys in environment variables')
    }

    return <ClientConfigProvider value={config}>{children}</ClientConfigProvider>
}
