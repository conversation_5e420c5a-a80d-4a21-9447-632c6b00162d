"use client"

import { createContext, use<PERSON>ontext, ReactNode } from "react"

type ClientConfig = {
  pusherKey: string
  pusherCluster: string
}

const ConfigContext = createContext<ClientConfig | null>(null)

export function useClientConfig(): ClientConfig {
  const context = useContext(ConfigContext)
  if (!context) {
    throw new Error("useClientConfig must be used within a ConfigProvider")
  }
  return context
}

export function ClientConfigProvider({
  value,
  children,
}: {
  value: ClientConfig
  children: ReactNode
}) {
  return (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
  )
}
