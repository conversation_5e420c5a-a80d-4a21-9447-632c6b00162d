{"api": {"ai_rule": {"create_failed": "Failed to create AI rule. Please try again.", "created": "AI rule created successfully", "delete_failed": "Failed to delete AI rule. Please try again.", "deleted": "AI rule deleted successfully", "duplicate_name": "AI rule with this name already exists", "fetch_failed": "Failed to fetch AI rules. Please try again.", "not_found": "AI rule not found", "update_failed": "Failed to update AI rule. Please try again.", "updated": "AI rule updated successfully"}, "contact": {"create_failed": "Failed to create contact. Please try again.", "created": "Contact created successfully", "delete_failed": "Failed to delete contact. Please try again.", "deleted": "Contact deleted successfully", "duplicate_name": "Contact with this name already exists", "duplicate_phone": "Contact with this phone number already exists", "fetch_failed": "Failed to fetch contacts. Please try again.", "not_found": "Contact not found", "update_failed": "Failed to update contact. Please try again.", "updated": "Contact updated successfully"}, "error": {"atleast_2_characters": "Name must be at least 2 characters", "bulk_delete_failed": "Bulk delete failed", "bulk_import_failed": "Bulk import failed", "bulk_update_failed": "Bulk update failed", "create_failed": "Failed to create resource. Please try again.", "database_error": "Database error occurred", "delete_failed": "Failed to delete resource. Please try again.", "duplicate_email": "Email already exists", "duplicate_name": "Name already exists", "duplicate_phone": "Phone number already exists", "duplicate_resource": "Resource already exists", "fetch_failed": "Failed to fetch resource. Please try again.", "file_not_found": "File not found", "file_too_large": "File too large", "file_upload_failed": "File upload failed", "filter_field_empty": "Filter field cannot be empty", "forbidden": "Access forbidden", "internal_server_error": "Internal server error", "invalid_credentials": "Invalid credentials", "invalid_data_format": "Invalid data format - expected array", "invalid_email": "Invalid email format", "invalid_email_format": "Invalid email format", "invalid_file_format": "Invalid file format", "invalid_id": "Invalid ID format", "invalid_phone": "Invalid phone number format", "invalid_phone_format": "Invalid phone number format", "invalid_sort_direction": "Sort direction must be 'asc' or 'desc'", "invalid_token": "Invalid or expired token", "invalid_update_data": "Invalid update data", "login_failed": "<PERSON><PERSON> failed. Please check your credentials.", "logout_failed": "<PERSON><PERSON><PERSON> failed. Please try again.", "name_must_not_contain_special_char": "Name must not contain special characters", "name_must_not_empty": "Name must not be empty", "network_error": "Network error occurred", "no_data_provided": "No data provided", "not_found": "Resource not found", "search_keyword_empty": "Search keyword cannot be empty", "session_expired": "Session has expired", "session_required": "Session required", "sort_field_empty": "Sort field cannot be empty", "unauthorized": "Unauthorized access", "update_failed": "Failed to update resource. Please try again.", "validation_failed": "Validation failed"}, "filter": {"invalid_operator": "Invalid filter operator", "invalid_value": "Invalid filter value", "unsupported_field": "Unsupported filter field"}, "message_template": {"create_failed": "Failed to create message template. Please try again.", "created": "Message template created successfully", "delete_failed": "Failed to delete message template. Please try again.", "deleted": "Message template deleted successfully", "duplicate_name": "Message template with this name already exists", "fetch_failed": "Failed to fetch message templates. Please try again.", "not_found": "Message template not found", "update_failed": "Failed to update message template. Please try again.", "updated": "Message template updated successfully"}, "search": {"invalid_query": "Invalid search query", "no_results": "No results found", "query_too_long": "Search query too long", "query_too_short": "Search query too short"}, "search_config": {"boolean": {"no": "No", "yes": "Yes"}, "contact": {"created_by": "Created By", "created_date": "Created Date", "email": "Email Address", "has_email": "<PERSON>", "has_notes": "Has Notes", "has_phone": "Has Phone Number", "name": "Name", "phone": "Phone Number", "status": "Status", "tags": "Tags", "updated_date": "Last Updated"}, "created_by": {"admin": "Admin", "api": "API", "import": "Import", "system": "System", "user": "User"}, "date": {"all": "All Time", "all_desc": "All contacts", "custom": "Custom Range", "custom_desc": "Select custom date range", "last_month": "Last Month", "last_month_desc": "Contacts created last month", "last_week": "Last Week", "last_week_desc": "Contacts created last week", "last_year": "Last Year", "last_year_desc": "Contacts created last year", "this_month": "This Month", "this_month_desc": "Contacts created this month", "this_week": "This Week", "this_week_desc": "Contacts created this week", "this_year": "This Year", "this_year_desc": "Contacts created this year", "today": "Today", "today_desc": "Contacts created today", "yesterday": "Yesterday", "yesterday_desc": "Contacts created yesterday"}, "placeholder": {"email": "Search by email address...", "name": "Search by contact name...", "phone": "Search by phone number..."}, "sales": {"amount": "Sale Amount", "customer_email": "Customer <PERSON><PERSON>", "customer_name": "Customer Name", "delivery_date": "Delivery Date", "has_discount": "Has Discount", "is_recurring": "Recurring Sale", "payment_method": "Payment Method", "product_name": "Product Name", "region": "Sales Region", "sale_date": "Sale Date", "sales_rep": "Sales Representative", "status": "Order Status"}, "sort": {"created_date": "Created Date", "email": "Email", "name": "Name", "phone": "Phone", "status": "Status", "updated_date": "Updated Date"}, "status": {"active": "Active", "archived": "Archived", "deleted": "Deleted", "inactive": "Inactive", "pending": "Pending"}}, "success": {"bulk_delete_completed": "Bulk delete completed successfully", "bulk_import_completed": "Bulk import completed successfully", "bulk_update_completed": "Bulk update completed successfully", "created": "Resource created successfully", "deleted": "Resource deleted successfully", "email_verified": "Email verified successfully", "fetched": "Resource fetched successfully", "logged_in": "Logged in successfully", "logged_out": "Logged out successfully", "password_changed": "Password changed successfully", "password_reset_sent": "Password reset email sent successfully", "updated": "Resource updated successfully", "verification_sent": "Verification email sent successfully"}, "user": {"create_failed": "Failed to create user. Please try again.", "created": "User created successfully", "delete_failed": "Failed to delete user. Please try again.", "deleted": "User deleted successfully", "duplicate_email": "User with this email already exists", "fetch_failed": "Failed to fetch users. Please try again.", "not_found": "User not found", "update_failed": "Failed to update user. Please try again.", "updated": "User updated successfully"}}}