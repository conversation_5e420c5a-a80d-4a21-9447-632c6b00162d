import { NextRequest, NextResponse } from "next/server"
import { providers } from "@/lib/providers"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { BroadcastRecipientStatus } from "@/lib/repositories/broadcast/interface"
import { BroadcastSendMessage } from "@/lib/queue/MessageQueue"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function POST(req: NextRequest) {
  try {
    const { broadcastBusinessLogic, broadcastRecipientBusinessLogic, contactsBusinessLogic, devicesBusinessLogic } = getBusinessLogics()
    const body: BroadcastSendMessage = await req.json()

    if (body.type !== "broadcast.send") {
      return NextResponse.json(
        { success: false, error: "Invalid message type" },
        { status: 400 }
      )
    }

    const { payload, context: bodyContext } = body
    const {
      broadcastId,
      broadcastRecipientId,
    } = payload

    const context: SessionContext = {
      user: {
        id: bodyContext.userId,
        name: "",
        email: ""
      },
      organization: bodyContext.organizationId ? { id: bodyContext.organizationId } : undefined,
    }

    if (!broadcastId || !broadcastRecipientId) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Missing required fields"],
          ["MISSING_REQUIRED_FIELDS"],
        ),
        { status: 400 }
      )
    }

    const broadcast = await broadcastBusinessLogic.getById(broadcastId, context)
    if (!broadcast) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast not found"],
          ["BROADCAST_NOT_FOUND"],
        ),
        { status: 400 }
      )
    }
    const recipient = await broadcastRecipientBusinessLogic.getById(broadcastRecipientId, context)
    if (!recipient) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Broadcast recipient not found"],
          ["BROADCAST_RECIPIENT_NOT_FOUND"],
        ),
        { status: 400 }
      )
    }
    const contact = await contactsBusinessLogic.getById(recipient.contactId, context)
    if (!contact) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          ["CONTACT_NOT_FOUND"],
        ),
        { status: 400 }
      )
    }

    const device = await devicesBusinessLogic.getById(broadcast.deviceId, context)
    if (!device) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          ["DEVICE_NOT_FOUND"],
        ),
        { status: 400 }
      )
    }

    // Get the provider
    const provider = providers[process.env.WHATSAPP_PROVIDER!]
    if (!provider) {
      console.error("Provider not available")

      // Mark recipient as failed
      await broadcastRecipientBusinessLogic.update(
        broadcastRecipientId,
        {
          status: BroadcastRecipientStatus.FAILED,
          failedAt: new Date(),
          errorMessage: "Provider not available",
        },
        context
      )

      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Provider not available"],
          ["PROVIDER_NOT_AVAILABLE"],
        ),
        { status: 500 }
      )
    }

    try {
      // Use contact phone as conversation ID for WhatsApp
      // const conversationId = contactPhone || contactId

      // Send message using provider
      await provider.sendMessage(contact.phone, broadcast.message, device.sessionId)

      // Mark as sent
      await broadcastRecipientBusinessLogic.update(
        broadcastRecipientId,
        {
          status: BroadcastRecipientStatus.SENT,
          sentAt: new Date(),
        },
        context
      )

      console.log(`Message sent successfully for broadcast ${broadcastId}, recipient ${broadcastRecipientId}`)

      return NextResponse.json(new ResponseWrapper("success", {
        broadcastId,
        broadcastRecipientId,
        contactId: contact.id,
        status: "sent",
      }))
    } catch (error: any) {
      console.error(`Failed to send message for broadcast ${broadcastId}, recipient ${broadcastRecipientId}:`, error)

      // Mark as failed
      await broadcastRecipientBusinessLogic.update(
        broadcastRecipientId,
        {
          status: BroadcastRecipientStatus.FAILED,
          failedAt: new Date(),
          errorMessage: error.message || "Failed to send message",
        },
        context
      )

      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Failed to send message"],
          ["FAILED_TO_SEND_MESSAGE"],
        ),
        { status: 500 }
      )
    }
  } catch (error: any) {
    console.error("Error processing broadcast sender request:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        ["INTERNAL_SERVER_ERROR"],
      ),
      { status: 500 }
    )
  }
}
