import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { implHandleSendMessageAIManual } from "./impl"

export async function POST(req: NextRequest) {
    try {
        const body = await req.json()

        const { context, response } = await buildSessionContext(req)
        if (response) {
            return response
        }

        const result = await implHandleSendMessageAIManual(body, context)
        return NextResponse.json(result.body, { status: result.status })
    } catch (error) {
        console.error("Send message route error:", error)
        return NextResponse.json(
            {
                status: "failed",
                data: null,
                error: "Internal server error",
                errorCodes: [],
            },
            { status: 500 },
        )
    }
}
