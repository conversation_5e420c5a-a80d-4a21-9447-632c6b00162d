import { loadBusinessConfig } from "@/lib/config/business-config"
import { cookies } from "next/headers"
import Home from "./client"
import { kDEFAULT_LANG } from "./constant"

export default async function Page() {
  const cookieStore = await cookies()
  const locale = cookieStore.get("locale")?.value || kDEFAULT_LANG
  const business = await loadBusinessConfig(locale)
  return <Home business={business} />
}
