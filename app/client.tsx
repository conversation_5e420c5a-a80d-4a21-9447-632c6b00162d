"use client"

import { useLocalization } from "@/localization/functions/client"
import { homeLocales } from "./locales"
import Link from "next/link"
import { LanguageSelector } from "@/components/ui/language-selector"
import { getAppVersion } from "@/lib/utils/version"
import {
  BusinessProvider,
  useBusinessContext,
} from "@/components/contexts/useBusinessContext"
import { BusinessConfig } from "@/lib/config/business-config"

export default function Home({ business }: { business: BusinessConfig }) {
  return (
    <BusinessProvider business={business}>
      <HomeClient />
    </BusinessProvider>
  )
}

function HomeClient() {
  const { t } = useLocalization("home", homeLocales)
  const { business } = useBusinessContext()
  return (
    <div className="min-h-screen flex flex-col justify-between bg-white relative">
      <div className="absolute top-3 right-3">
        <LanguageSelector />
      </div>
      {/* Hero Section */}
      <main className="flex-1 flex flex-col items-center justify-center px-4 text-center">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">
          {t("hero_title", {
            businessInfoName: business.businessInfo.name,
          })}
        </h1>
        <p className="text-lg text-gray-600 max-w-xl mb-8">
          {t("hero_desc", {
            businessInfoName: business.businessInfo.name,
          })}
        </p>

        {/* Login Button */}
        <Link href="/auth/login">
          <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition">
            {t("login_button")}
          </button>
        </Link>
      </main>
      {/* Footer (optional) */}
      <footer className="flex flex-col text-center py-4 text-sm text-gray-500 ">
        <span>
          © {new Date().getFullYear()} CS AI. {t("footer_copyright")}
        </span>
        <span>{getAppVersion()}</span>
      </footer>
    </div>
  )
}
