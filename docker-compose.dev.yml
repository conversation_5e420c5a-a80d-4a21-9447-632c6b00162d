version: "3.8"

services:
  cs-ai-app:
    image: ${CS_AI_APP_IMAGE:-cs-ai-app:latest}
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${DOCKER_EXPOSED_PORT:-3000}:3000"
    env_file:
      - .env.dev
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - cs-ai-network
    volumes:
      # Optional: Mount logs directory if your app writes logs
      - ./logs:/app/logs

    # 👇 Add this block to allow containers to resolve host.docker.internal (Linux only)
    extra_hosts:
      # On Linux, this maps 'host.docker.internal' to the special gateway IP for the Docker host.
      # This makes it possible to access services running on the host machine (e.g., <PERSON><PERSON>, Loki, etc.)
      - "host.docker.internal:host-gateway"

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  cs-ai-network:
    name: cs-ai-network
    external: true

volumes:
  rabbitmq_data:
