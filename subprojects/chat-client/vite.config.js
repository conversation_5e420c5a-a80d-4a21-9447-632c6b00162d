import { sveltekit } from "@sveltejs/kit/vite"
import { defineConfig, loadEnv } from "vite"

export default defineConfig(({ mode }) => {
  // Load environment variables from .env.local
  const env = loadEnv(mode, process.cwd(), "")

  return {
    plugins: [sveltekit()],
    server: {
      allowedHosts: ["t470", "chat-client-dev.kerjaremoteluarnegeri.com"],
    },
    define: {
      "process.env.MONGODB_URI": JSON.stringify(env.MONGODB_URI),
      "process.env.MONGODB_DATABASE": JSON.stringify(env.MONGODB_DATABASE),
    },
  }
})
