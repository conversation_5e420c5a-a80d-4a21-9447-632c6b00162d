<script lang="ts">
  import { onMount, onDestroy } from "svelte"
  import ConversationsList from "../lib/components/ConversationsList.svelte"
  import ChatPanel from "../lib/components/ChatPanel.svelte"
  import type { Conversation } from "../lib/types"
  import { api } from "../lib/api"
  import {
    sse,
    type SSEMessageEvent,
    type ConversationUpdateEvent,
  } from "../lib/sse"

  let conversations: Conversation[] = []
  let selectedConversation: Conversation | null = null
  let loading = true
  let error: string | null = null

  let unsubscribeMessage: (() => void) | null = null
  let unsubscribeConversationUpdate: (() => void) | null = null

  async function loadConversations(): Promise<void> {
    try {
      loading = true
      error = null
      const response = await api.getConversations()

      if (response.status === "success") {
        conversations = response.data.items
      } else {
        error = "Failed to load conversations"
      }
    } catch (err) {
      console.error("Error loading conversations:", err)
      error =
        err instanceof Error ? err.message : "Failed to load conversations"
    } finally {
      loading = false
    }
  }

  async function loadMessages(conversationId: string): Promise<void> {
    try {
      console.log("Loading messages for conversation:", conversationId)
      const response = await api.getMessages(conversationId)

      if (response.status === "success") {
        console.log("Loaded messages:", response.data.messages.length)
        const conversationIndex = conversations.findIndex(
          (conv) => conv.id === conversationId,
        )
        if (conversationIndex > -1) {
          const updatedConversation = {
            ...conversations[conversationIndex],
            messages: response.data.messages,
          }

          conversations[conversationIndex] = updatedConversation
          conversations = [...conversations]

          if (selectedConversation?.id === conversationId) {
            selectedConversation = { ...updatedConversation }
            console.log(
              "Updated selected conversation with loaded messages. Total:",
              selectedConversation.messages.length,
            )
          }
        }
      }
    } catch (err) {
      console.error("Error loading messages:", err)
    }
  }

  function setupSSE(): void {
    sse.connect()

    unsubscribeMessage = sse.onMessage((event: SSEMessageEvent) => {
      const { message, conversation } = event.data

      console.log(
        "SSE: Received new message:",
        message.text,
        "for conversation:",
        conversation.id,
      )

      const conversationIndex = conversations.findIndex(
        (conv) => conv.id === conversation.id,
      )
      if (conversationIndex > -1) {
        const existingMessages = conversations[conversationIndex].messages || []

        const isDuplicate = existingMessages.some((m) => m.id === message.id)

        if (isDuplicate) {
          console.warn("⚠️ Duplicate message detected, skipping:", message.id)
          return
        }

        const updatedConversation = {
          ...conversations[conversationIndex],
          messages: [...existingMessages, message],
          lastMessage: message.text,
          timestamp: message.timestamp,
          updatedAt: new Date(),
        }

        conversations.splice(conversationIndex, 1)
        conversations.unshift(updatedConversation)

        if (selectedConversation?.id === conversation.id) {
          selectedConversation = { ...updatedConversation }
          console.log(
            "SSE: Updated selected conversation with new message. Total messages:",
            selectedConversation.messages.length,
          )
        }

        conversations = [...conversations]
        console.log(
          "SSE: Conversation updated in list. Total conversations:",
          conversations.length,
        )
      } else {
        console.log("SSE: Conversation not found in list:", conversation.id)
      }
    })

    unsubscribeConversationUpdate = sse.onConversationUpdate(
      (event: ConversationUpdateEvent) => {
        const { conversation, action } = event.data

        if (action === "created") {
          conversations.unshift(conversation)
          conversations = [...conversations]
        } else if (action === "updated") {
          return
          const conversationIndex = conversations.findIndex(
            (conv) => conv.id === conversation.id,
          )
          if (conversationIndex > -1) {
            conversations[conversationIndex] = {
              ...conversations[conversationIndex],
              ...conversation,
            }
            conversations = [...conversations]

            if (selectedConversation?.id === conversation.id) {
              selectedConversation = conversations[conversationIndex]
            }
          }
        } else if (action === "deleted") {
          conversations = conversations.filter(
            (conv) => conv.id !== conversation.id,
          )

          if (selectedConversation?.id === conversation.id) {
            selectedConversation = null
          }
        }
      },
    )
  }

  onMount(async () => {
    await loadConversations()
    setupSSE()
  })

  onDestroy(() => {
    if (unsubscribeMessage) unsubscribeMessage()
    if (unsubscribeConversationUpdate) unsubscribeConversationUpdate()
    sse.disconnect()
  })

  async function selectConversation(conversation: Conversation): Promise<void> {
    console.log(
      "Selecting conversation:",
      conversation.id,
      "Current messages:",
      conversation.messages?.length || 0,
    )

    selectedConversation = { ...conversation }

    if (!conversation.messages || conversation.messages.length === 0) {
      await loadMessages(conversation.id)
    }

    try {
      await api.markAsRead(conversation.id)
    } catch (err) {
      console.error("Error marking conversation as read:", err)
    }
  }

  async function addNewConversation(
    event: CustomEvent<Conversation>,
  ): Promise<void> {
    const newConversationData = event.detail

    try {
      const response = await api.createConversation(newConversationData)

      if (response.status === "success") {
        const newConversation = response.data
        conversations = [newConversation, ...conversations]
        selectedConversation = newConversation
      }
    } catch (err) {
      console.error("Error creating conversation:", err)
    }
  }

  async function updateConversation(
    event: CustomEvent<{ id: string; [key: string]: any }>,
  ): Promise<void> {
    const { id, ...updates } = event.detail

    try {
      const response = await api.updateConversation(id, updates)

      if (response.status === "success") {
        const updatedConversation = response.data

        conversations = conversations.map((conv) => {
          if (conv.id === id) {
            if (selectedConversation?.id === id) {
              selectedConversation = updatedConversation
            }
            return updatedConversation
          }
          return conv
        })
      }
    } catch (err) {
      console.error("Error updating conversation:", err)
    }
  }

  async function sendMessage(
    event: CustomEvent<{ text: string }>,
  ): Promise<void> {
    const { text } = event.detail

    if (selectedConversation && text.trim()) {
      try {
        console.log(
          "Sending message:",
          text.trim(),
          "to conversation:",
          selectedConversation.id,
        )

        const response = await api.sendMessage(
          selectedConversation.id,
          text.trim(),
        )

        if (response.status === "success") {
          console.log("Message sent successfully, waiting for SSE update")
        } else {
          console.error("Failed to send message:", response)
        }
      } catch (err) {
        console.error("Error sending message:", err)
      }
    } else {
      console.log("Cannot send message: no conversation selected or empty text")
    }
  }

  async function deleteConversation(
    event: CustomEvent<{ id: string }>,
  ): Promise<void> {
    const { id } = event.detail

    try {
      console.log("Deleting conversation:", id)

      const response = await api.deleteConversation(id)

      if (response.status === "success") {
        conversations = conversations.filter((conv) => conv.id !== id)

        if (selectedConversation?.id === id) {
          selectedConversation = null
        }
      } else {
        console.error("Failed to delete conversation:", response)
      }
    } catch (err) {
      console.error("Error deleting conversation:", err)
    }
  }

  async function clearMessages(
    event: CustomEvent<{ id: string }>,
  ): Promise<void> {
    const { id } = event.detail

    try {
      console.log("Clearing messages for conversation:", id)

      const response = await api.clearMessages(id)

      if (response.status === "success") {
        const conversationIndex = conversations.findIndex(
          (conv) => conv.id === id,
        )
        if (conversationIndex > -1) {
          const updatedConversation = {
            ...conversations[conversationIndex],
            messages: [],
            lastMessage: "",
            updatedAt: new Date(),
          }

          conversations[conversationIndex] = updatedConversation
          conversations = [...conversations]

          if (selectedConversation?.id === id) {
            selectedConversation = { ...updatedConversation }
            console.log("Cleared messages from selected conversation")
          }
        }
      } else {
        console.error("Failed to clear messages:", response)
      }
    } catch (err) {
      console.error("Error clearing messages:", err)
    }
  }

  async function duplicateConversation(
    event: CustomEvent<Conversation>,
  ): Promise<void> {
    const duplicatedConversation = event.detail

    try {
      console.log("Duplicating conversation:", duplicatedConversation.name)

      const response = await api.createConversation(duplicatedConversation)

      if (response.status === "success") {
        console.log("Conversation duplicated successfully")

        // Add the duplicated conversation to the list at the top
        conversations.unshift(response.data)
        conversations = [...conversations] // Trigger reactivity

        // Optionally select the new duplicated conversation
        selectedConversation = { ...response.data }
      } else {
        console.error("Failed to duplicate conversation:", response)
      }
    } catch (err) {
      console.error("Error duplicating conversation:", err)
    }
  }
</script>

{#if loading}
  <div class="flex items-center justify-center h-screen">
    <div class="text-center">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
      ></div>
      <p class="text-gray-600">Loading conversations...</p>
    </div>
  </div>
{:else if error}
  <div class="flex items-center justify-center h-screen">
    <div class="text-center">
      <div class="text-red-500 text-xl mb-4">⚠️</div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        Error Loading Conversations
      </h3>
      <p class="text-gray-600 mb-4">{error}</p>
      <button
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        on:click={loadConversations}
      >
        Retry
      </button>
    </div>
  </div>
{:else}
  <div
    class="chat-container"
    id="chat-container"
    data-identifier="chat-container"
  >
    <ConversationsList
      {conversations}
      {selectedConversation}
      on:select={(e) => selectConversation(e.detail)}
      on:newConversation={addNewConversation}
      on:updateConversation={updateConversation}
      on:deleteConversation={deleteConversation}
      on:duplicateConversation={duplicateConversation}
    />

    {#if selectedConversation}
      <ChatPanel
        conversation={selectedConversation}
        on:sendMessage={sendMessage}
        on:updateConversation={updateConversation}
        on:clearMessages={clearMessages}
        on:deleteConversation={deleteConversation}
        on:duplicateConversation={duplicateConversation}
      />
    {:else}
      <div class="empty-state">
        <h3>Select a conversation to start chatting</h3>
        <p>Choose a conversation from the list to view messages</p>
        {#if !sse.isConnected()}
          <p class="text-yellow-600 mt-2">⚠️ Real-time updates disconnected</p>
        {/if}
      </div>
    {/if}
  </div>
{/if}
