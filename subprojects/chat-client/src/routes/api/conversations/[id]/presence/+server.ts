import { json, error } from "@sveltejs/kit"
import type { RequestHandler } from "./$types"

// Mock presence storage
let presenceData: Record<string, any> = {}

export const POST: RequestHandler = async ({ params, request }) => {
  const body = await request.json()

  // Store presence data for the conversation/session
  presenceData[params.id] = {
    presence: body.presence,
    timestamp: body.timestamp || new Date().toISOString(),
    sessionId: params.id,
  }

  return json({
    status: "success",
    data: {
      message: "Presence updated successfully",
      presence: body.presence,
      sessionId: params.id,
    },
  })
}

export const GET: RequestHandler = async ({ params }) => {
  const presence = presenceData[params.id]

  if (!presence) {
    return json({
      status: "success",
      data: {
        presence: "offline",
        sessionId: params.id,
        timestamp: new Date().toISOString(),
      },
    })
  }

  return json({
    status: "success",
    data: presence,
  })
}
