import { json, error } from "@sveltejs/kit"
import type { RequestHandler } from "./$types"

// Mock read status storage
let readStatus: Record<string, any> = {}

export const POST: RequestHandler = async ({ params, request }) => {
  const body = await request.json()

  // Mark messages as read for the conversation
  readStatus[params.id] = {
    conversationId: body.conversationId || params.id,
    readAt: new Date().toISOString(),
    sessionId: params.id,
  }

  return json({
    status: "success",
    data: {
      message: "Messages marked as read",
      conversationId: body.conversationId || params.id,
      sessionId: params.id,
      readAt: readStatus[params.id].readAt,
    },
  })
}

export const GET: RequestHandler = async ({ params }) => {
  const status = readStatus[params.id]

  return json({
    status: "success",
    data: status || {
      conversationId: params.id,
      sessionId: params.id,
      readAt: null,
    },
  })
}
