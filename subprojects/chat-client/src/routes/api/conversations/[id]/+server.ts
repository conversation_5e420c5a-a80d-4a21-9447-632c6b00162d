import { json, error } from "@sveltejs/kit"
import type { RequestHand<PERSON> } from "./$types"
import { ConversationService } from "../../../../lib/mongodb"

export const GET: RequestHandler = async ({ params }) => {
  try {
    const conversation = await ConversationService.getConversation(params.id)

    if (!conversation) {
      throw error(404, "Conversation not found")
    }

    return json({
      status: "success",
      data: conversation,
    })
  } catch (err) {
    console.error("Error fetching conversation:", err)
    throw error(500, "Internal server error")
  }
}

export const PUT: RequestHandler = async ({ params, request }) => {
  try {
    const body = await request.json()

    const updatedConversation = await ConversationService.updateConversation(
      params.id,
      body,
    )

    if (!updatedConversation) {
      throw error(404, "Conversation not found")
    }

    return json({
      status: "success",
      data: updatedConversation,
    })
  } catch (err) {
    console.error("Error updating conversation:", err)
    throw error(500, "Internal server error")
  }
}

export const DELETE: RequestHandler = async ({ params }) => {
  try {
    const deleted = await ConversationService.deleteConversation(params.id)

    if (!deleted) {
      throw error(404, "Conversation not found")
    }

    return json({
      status: "success",
      data: { deleted: true },
    })
  } catch (err) {
    console.error("Error deleting conversation:", err)
    throw error(500, "Internal server error")
  }
}
