import { json } from "@sveltejs/kit"
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from "./$types"
import type { Conversation } from "../../../lib/types"
import { ConversationService } from "../../../lib/mongodb"

export const GET: RequestHandler = async ({ url }) => {
  try {
    const page = parseInt(url.searchParams.get("page") || "1")
    const per_page = parseInt(url.searchParams.get("per_page") || "10")
    const search = url.searchParams.get("search") || undefined

    const { items, total } = await ConversationService.getConversations(
      page,
      per_page,
      search,
    )

    return json({
      status: "success",
      data: {
        items,
        total,
        page,
        per_page,
        syncResult: {
          synced: items.length,
          created: 0,
          updated: 0,
          errors: [],
        },
      },
    })
  } catch (error) {
    console.error("Error fetching conversations:", error)
    return json(
      {
        status: "failed",
        data: {
          items: [],
          total: 0,
          page: 1,
          per_page: 10,
          syncResult: {
            synced: 0,
            created: 0,
            updated: 0,
            errors: [error instanceof Error ? error.message : "Unknown error"],
          },
        },
      },
      { status: 500 },
    )
  }
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()

    if (body.action === "sync") {
      // Sync operation - return current conversations count
      const { total } = await ConversationService.getConversations(1, 1)
      return json({
        status: "success",
        data: {
          synced: total,
          created: 0,
          updated: 0,
          errors: [],
        },
      })
    }

    // Create new conversation
    const newConversation = await ConversationService.createConversation({
      sessionId: body.sessionId,
      name: body.name,
      from: body.from,
      to: body.to,
      status: body.status,
    })

    return json(
      {
        status: "success",
        data: newConversation,
      },
      { status: 201 },
    )
  } catch (error) {
    console.error("Error creating conversation:", error)
    return json(
      {
        status: "failed",
        data: null,
        messages: [error instanceof Error ? error.message : "Unknown error"],
      },
      { status: 500 },
    )
  }
}
