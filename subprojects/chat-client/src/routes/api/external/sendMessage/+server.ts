import { json, error } from "@sveltejs/kit"
import type { RequestHand<PERSON> } from "./$types"
import { ConversationService, MessageService } from "../../../../lib/mongodb"
import { broadcastEvent } from "../../../../lib/sse-utils"

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()

    console.log("RECEIEVE EXTERNAL MESSAGE", body)

    // Validate required fields
    if (!body.text || !body.conversationId) {
      throw error(400, "Missing required fields: text and conversationId")
    }

    const { text, conversationId: externalConversationId, sessionId } = body

    const conversationId = externalConversationId.replace("@c.us", "")

    // Find or create conversation
    let conversation = await ConversationService.getConversation(conversationId)

    if (!conversation) {
      conversation = await ConversationService.findOrCreateConversation(
        conversationId,
        {
          sessionId,
          name: "External Initiated Conversation",
          from: "external",
          to: "external",
          status: "CONNECTED",
        },
      )
    }

    // Create new message
    const newMessage = await MessageService.createMessage(conversation.id, {
      text: text.trim(),
      fromMe: false, // Message from external source (cs-ai)
      timestamp: new Date(),
    })

    // Get updated conversation
    const updatedConversation = await ConversationService.getConversation(
      conversation.id,
    )

    // Broadcast new message event via SSE
    broadcastEvent({
      type: "message",
      data: {
        message: newMessage,
        conversation: {
          id: updatedConversation!.id,
          sessionId: updatedConversation!.sessionId,
          name: updatedConversation!.name,
          from: updatedConversation!.from,
          to: updatedConversation!.to,
          status: updatedConversation!.status,
          lastMessage: updatedConversation!.lastMessage,
          timestamp: updatedConversation!.timestamp,
        },
      },
      conversationId: updatedConversation!.id,
    })

    // Broadcast conversation update
    broadcastEvent({
      type: "conversation_update",
      data: {
        conversation: updatedConversation!,
        action: "updated",
      },
      conversationId: updatedConversation!.id,
    })

    return json(
      {
        status: "success",
        data: {
          message: newMessage,
          conversation: {
            id: updatedConversation!.id,
            sessionId: updatedConversation!.sessionId,
            name: updatedConversation!.name,
            from: updatedConversation!.from,
            to: updatedConversation!.to,
            status: updatedConversation!.status,
          },
        },
      },
      { status: 201 },
    )
  } catch (err) {
    console.error("Error processing external message:", err)

    if (err instanceof Error && "status" in err) {
      throw err
    }

    throw error(500, "Internal server error")
  }
}
