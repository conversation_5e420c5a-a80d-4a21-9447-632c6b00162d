import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import {
  addConnection,
  removeConnection,
  cleanupStaleConnections,
} from "../../../lib/sse-utils"

// Periodic cleanup of stale connections every 2 minutes
setInterval(() => {
  cleanupStaleConnections()
}, 120000)

export const GET: RequestHandler = async ({ request }) => {
  // Set up SSE headers
  const headers = new Headers({
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Cache-Control",
  })

  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      // Add this connection to our set
      addConnection(controller)

      // Send initial connection event
      const welcomeEvent = `event: connected\ndata: ${JSON.stringify({
        type: "connected",
        data: {
          message: "SSE connection established",
          timestamp: new Date().toISOString(),
        },
      })}\n\n`

      controller.enqueue(new TextEncoder().encode(welcomeEvent))

      // Send periodic heartbeat to keep connection alive
      const heartbeat = setInterval(() => {
        try {
          // Check if controller is still writable before sending heartbeat
          if (controller.desiredSize !== null) {
            const heartbeatEvent = `event: heartbeat\ndata: ${JSON.stringify({
              type: "heartbeat",
              data: { timestamp: new Date().toISOString() },
            })}\n\n`

            controller.enqueue(new TextEncoder().encode(heartbeatEvent))
          } else {
            // Controller is closed, clean up
            console.log("Heartbeat: Connection closed, cleaning up")
            clearInterval(heartbeat)
            removeConnection(controller)
          }
        } catch (error) {
          // Connection is closed or in invalid state, clean up
          console.log("Heartbeat: Connection error, cleaning up")
          clearInterval(heartbeat)
          removeConnection(controller)
        }
      }, 30000) // Send heartbeat every 30 seconds

      // Handle client disconnect
      request.signal.addEventListener("abort", () => {
        clearInterval(heartbeat)
        removeConnection(controller)
        controller.close()
      })
    },

    cancel() {
      // Clean up when stream is cancelled
      removeConnection(this as any)
    },
  })

  return new Response(stream, { headers })
}
