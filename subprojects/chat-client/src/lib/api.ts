import type { Conversation, Message, ApiResponse } from "./types"

const API_BASE = "/api"

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${API_BASE}${endpoint}`

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Conversations
  async getConversations(
    page = 1,
    per_page = 50,
    search?: string,
  ): Promise<
    ApiResponse<{
      items: Conversation[]
      total: number
      page: number
      per_page: number
    }>
  > {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: per_page.toString(),
    })

    if (search) {
      params.append("search", search)
    }

    return this.request(`/conversations?${params}`)
  }

  async getConversation(id: string): Promise<ApiResponse<Conversation>> {
    return this.request(`/conversations/${id}`)
  }

  async createConversation(
    data: Partial<Conversation>,
  ): Promise<ApiResponse<Conversation>> {
    return this.request("/conversations", {
      method: "POST",
      body: JSON.stringify(data),
    })
  }

  async updateConversation(
    id: string,
    data: Partial<Conversation>,
  ): Promise<ApiResponse<Conversation>> {
    return this.request(`/conversations/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    })
  }

  async deleteConversation(
    id: string,
  ): Promise<ApiResponse<{ deleted: boolean }>> {
    return this.request(`/conversations/${id}`, {
      method: "DELETE",
    })
  }

  // Messages
  async getMessages(
    conversationId: string,
    limit = 50,
    offset = 0,
  ): Promise<
    ApiResponse<{
      conversation: Conversation
      messages: Message[]
      total: number
    }>
  > {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    })

    return this.request(`/conversations/${conversationId}/messages?${params}`)
  }

  async sendMessage(
    conversationId: string,
    text: string,
  ): Promise<ApiResponse<Message>> {
    return this.request(`/conversations/${conversationId}/messages`, {
      method: "POST",
      body: JSON.stringify({
        text,
        fromMe: true,
        timestamp: new Date().toISOString(),
      }),
    })
  }

  async clearMessages(
    conversationId: string,
  ): Promise<ApiResponse<{ cleared: boolean }>> {
    return this.request(`/conversations/${conversationId}/messages`, {
      method: "DELETE",
    })
  }

  // Presence and typing
  async updatePresence(
    conversationId: string,
    presence: string,
  ): Promise<ApiResponse<any>> {
    return this.request(`/conversations/${conversationId}/presence`, {
      method: "POST",
      body: JSON.stringify({
        presence,
        timestamp: new Date().toISOString(),
      }),
    })
  }

  async updateTyping(
    conversationId: string,
    status: "typing" | "stopped",
  ): Promise<ApiResponse<any>> {
    return this.request(`/conversations/${conversationId}/typing`, {
      method: "POST",
      body: JSON.stringify({
        status,
        conversationId,
        timestamp: new Date().toISOString(),
      }),
    })
  }

  async markAsRead(conversationId: string): Promise<ApiResponse<any>> {
    return this.request(`/conversations/${conversationId}/messages/read`, {
      method: "POST",
      body: JSON.stringify({
        conversationId,
      }),
    })
  }
}

export const api = new ApiService()
