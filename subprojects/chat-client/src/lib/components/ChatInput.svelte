<script lang="ts">
  import { createEventDispatcher } from "svelte"

  const dispatch = createEventDispatcher<{
    sendMessage: { text: string }
  }>()

  let inputText = ""
  let inputElement: HTMLInputElement

  function handleSubmit(event: Event): void {
    event.preventDefault()

    if (inputText.trim()) {
      dispatch("sendMessage", { text: inputText })
      inputText = ""
      inputElement.focus()
    }
  }

  function handleKeydown(event: KeyboardEvent): void {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      handleSubmit(event)
    }
  }
</script>

<div
  class="chat-input-container"
  id="chat-input-container"
  data-identifier="chat-input-container"
>
  <form
    class="chat-input-form"
    id="chat-input-form"
    data-identifier="chat-input-form"
    on:submit={handleSubmit}
  >
    <input
      bind:this={inputElement}
      bind:value={inputText}
      on:keydown={handleKeydown}
      class="chat-input"
      id="chat-input"
      data-identifier="chat-input"
      type="text"
      placeholder="Type a message..."
      autocomplete="off"
    />
    <button
      type="submit"
      class="send-button"
      id="send-button"
      data-identifier="send-button"
      disabled={!inputText.trim()}
    >
      Send
    </button>
  </form>
</div>
