<script lang="ts">
  import { createEventDispatcher } from "svelte"

  import type { Message, Conversation } from "../types"

  export let conversations: Conversation[] = []
  export let selectedConversation: Conversation | null = null

  const dispatch = createEventDispatcher<{
    select: Conversation
    newConversation: Conversation
    updateConversation: { id: string; [key: string]: any }
    deleteConversation: { id: string }
    duplicateConversation: Conversation
  }>()

  let showNewConversationDialog = false
  let newConversationName = ""
  let newConversationSessionId = ""
  let newConversationFrom = ""
  let newConversationTo = ""

  let editingConversationId: string | null = null
  let tempConversationName = ""

  function selectConversation(conversation: Conversation): void {
    dispatch("select", conversation)
  }

  function startEditingName(
    conversation: Conversation,
    event: MouseEvent,
  ): void {
    event.stopPropagation()
    editingConversationId = conversation.id
    tempConversationName = conversation.name
  }

  function saveConversationName(conversation: Conversation): void {
    if (tempConversationName.trim()) {
      dispatch("updateConversation", {
        id: conversation.id,
        name: tempConversationName.trim(),
      })
    }
    editingConversationId = null
    tempConversationName = ""
  }

  function cancelEditingName(): void {
    editingConversationId = null
    tempConversationName = ""
  }

  function deleteConversation(
    conversation: Conversation,
    event: MouseEvent,
  ): void {
    event.stopPropagation()

    if (
      confirm(
        `Are you sure you want to delete the conversation with "${conversation.name}"? This action cannot be undone.`,
      )
    ) {
      dispatch("deleteConversation", { id: conversation.id })
    }
  }

  function duplicateConversation(
    conversation: Conversation,
    event: MouseEvent,
  ): void {
    event.stopPropagation()

    // Create a duplicate conversation with same TO and sessionId but different FROM and name
    const duplicatedConversation: Conversation = {
      id: Date.now().toString(), // New unique ID
      sessionId: conversation.sessionId, // Keep same session ID
      name: `${conversation.name} (Copy)`, // Add "(Copy)" to name
      from: `${conversation.from}_copy`, // Modify FROM to make it unique
      to: conversation.to, // Keep same TO
      status: conversation.status,
      lastMessage: "",
      timestamp: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      messages: [], // Start with empty messages
    }

    dispatch("duplicateConversation", duplicatedConversation)
  }

  function handleNameKeydown(
    event: KeyboardEvent,
    conversation: Conversation,
  ): void {
    if (event.key === "Enter") {
      event.preventDefault()
      saveConversationName(conversation)
    } else if (event.key === "Escape") {
      cancelEditingName()
    }
  }

  function openNewConversationDialog(): void {
    showNewConversationDialog = true
    newConversationFrom = ""
    newConversationTo = ""
  }

  function closeNewConversationDialog(): void {
    showNewConversationDialog = false
  }

  function createNewConversation(): void {
    if (
      newConversationFrom.trim() &&
      newConversationTo.trim() &&
      newConversationName.trim() &&
      newConversationSessionId.trim()
    ) {
      const newConversation: Conversation = {
        id: Date.now().toString(),
        name: newConversationName.trim(),
        sessionId: newConversationSessionId.trim(),
        from: newConversationFrom.trim(),
        to: newConversationTo.trim(),
        lastMessage: "",
        timestamp: new Date(),
        messages: [],
      }

      dispatch("newConversation", newConversation)
      closeNewConversationDialog()
    }
  }

  function formatTime(timestamp: string | number | Date): string {
    const now = new Date()
    const date = new Date(timestamp)
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      })
    } else if (diffInHours < 168) {
      return date.toLocaleDateString("en-US", { weekday: "short" })
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })
    }
  }
</script>

<!-- MAIN PANEL -->
<div class="conversations-panel" id="conversations-panel">
  <div class="conversations-header">
    <h2 class="conversations-title">Conversations</h2>
    <button class="new-conversation-btn" on:click={openNewConversationDialog}
      >+ New</button
    >
  </div>

  <div class="conversations-list">
    {#each conversations as conversation (conversation.id)}
      <div
        class="conversation-item {selectedConversation?.id === conversation.id
          ? 'active'
          : ''}"
        role="button"
        tabindex="0"
        on:click={() => selectConversation(conversation)}
        on:keydown={(e) =>
          e.key === "Enter" && selectConversation(conversation)}
      >
        <div class="flex items-center justify-between mb-1">
          {#if editingConversationId === conversation.id}
            <input
              type="text"
              class="edit-name-input flex-1"
              bind:value={tempConversationName}
              on:keydown={(e) => handleNameKeydown(e, conversation)}
              on:blur={() => saveConversationName(conversation)}
              on:click|stopPropagation
              autofocus
            />
            <div class="edit-buttons">
              <button
                class="edit-btn save"
                on:click|stopPropagation={() =>
                  saveConversationName(conversation)}>✓</button
              >
              <button
                class="edit-btn cancel"
                on:click|stopPropagation={cancelEditingName}>✕</button
              >
            </div>
          {:else}
            <span class="conversation-name">{conversation.name}</span>
            <div class="conversation-actions">
              <button
                class="action-btn edit-btn"
                title="Edit conversation name"
                on:click|stopPropagation={(e) =>
                  startEditingName(conversation, e)}>✎</button
              >
              <button
                class="action-btn duplicate-btn"
                title="Duplicate conversation"
                on:click|stopPropagation={(e) =>
                  duplicateConversation(conversation, e)}>📋</button
              >
              <button
                class="action-btn delete-btn"
                title="Delete conversation"
                on:click|stopPropagation={(e) =>
                  deleteConversation(conversation, e)}>🗑️</button
              >
            </div>
          {/if}
        </div>

        <div class="conversation-from">FROM: {conversation.from}</div>
        <div class="conversation-from">
          TO: {conversation.to || conversation.name}
        </div>
        <div class="conversation-preview">{conversation.lastMessage}</div>
        <div class="conversation-time">
          {formatTime(conversation.timestamp)}
        </div>
      </div>
    {/each}

    {#if conversations.length === 0}
      <div class="empty-state">
        <h3>No conversations</h3>
        <p>Start a new conversation to get started</p>
      </div>
    {/if}
  </div>
</div>

<!-- DIALOG -->
{#if showNewConversationDialog}
  <div class="dialog-overlay" on:click={closeNewConversationDialog}>
    <div class="dialog" on:click|stopPropagation>
      <div class="dialog-header">
        <h3 class="dialog-title">New Conversation</h3>
        <button class="dialog-close" on:click={closeNewConversationDialog}
          >×</button
        >
      </div>

      <div class="dialog-body">
        <div class="dialog-field">
          <label class="dialog-label" for="name">NAME:</label>
          <input
            id="name"
            type="text"
            class="dialog-input"
            bind:value={newConversationName}
            placeholder="Conversation name"
          />
        </div>

        <div class="dialog-field">
          <label class="dialog-label" for="from">FROM:</label>
          <input
            id="from"
            type="text"
            class="dialog-input"
            bind:value={newConversationFrom}
            placeholder="Your number or ID"
          />
        </div>

        <div class="dialog-field">
          <label class="dialog-label" for="to">TO:</label>
          <input
            id="to"
            type="text"
            class="dialog-input"
            bind:value={newConversationTo}
            placeholder="Recipient’s number or ID"
          />
        </div>

        <div class="dialog-field">
          <label class="dialog-label" for="session">SESSION:</label>
          <input
            id="sessionId"
            type="text"
            class="dialog-input"
            bind:value={newConversationSessionId}
            placeholder="Your CS AI sessionID"
          />
        </div>
      </div>

      <div class="dialog-footer">
        <button
          class="dialog-btn secondary"
          on:click={closeNewConversationDialog}>Cancel</button
        >
        <button
          class="dialog-btn primary"
          on:click={createNewConversation}
          disabled={!newConversationFrom.trim() || !newConversationTo.trim()}
        >
          Create
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .conversation-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    text-align: right;
  }

  .conversations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .conversations-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .new-conversation-btn {
    background: #2196f3;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .new-conversation-btn:hover {
    background: #1976d2;
  }

  .conversation-to {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }

  .conversation-name-container {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
  }

  .conversation-name {
    flex: 1;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
  }

  .conversation-name:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .conversation-name-input {
    flex: 1;
    padding: 2px 6px;
    border: 1px solid #2196f3;
    border-radius: 3px;
    outline: none;
    font-size: 14px;
    font-weight: 600;
    background: white;
  }

  .name-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 10px;
    transition: background-color 0.2s;
    opacity: 0.7;
  }

  .name-btn:hover {
    opacity: 1;
  }

  .name-btn.edit {
    color: #666;
  }

  .name-btn.edit:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  .name-btn.save {
    color: #4caf50;
  }

  .name-btn.save:hover {
    background: #e8f5e8;
  }

  .name-btn.cancel {
    color: #f44336;
  }

  .name-btn.cancel:hover {
    background: #ffeaea;
  }

  /* Conversation Actions */
  .conversation-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .conversation-item:hover .conversation-actions {
    opacity: 1;
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
  }

  .action-btn:hover {
    transform: scale(1.1);
  }

  .action-btn.edit-btn {
    color: #666;
  }

  .action-btn.edit-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
  }

  .action-btn.duplicate-btn {
    color: #2196f3;
  }

  .action-btn.duplicate-btn:hover {
    background: #e3f2fd;
    color: #1976d2;
  }

  .action-btn.delete-btn {
    color: #f44336;
  }

  .action-btn.delete-btn:hover {
    background: #ffeaea;
    color: #d32f2f;
  }

  /* Dialog Styles */
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .dialog {
    background: white;
    border-radius: 8px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .dialog-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    color: #333;
  }

  .dialog-content {
    padding: 20px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .dialog-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
  }

  .dialog-input:focus {
    border-color: #2196f3;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e0e0e0;
  }

  .cancel-btn,
  .create-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;
  }

  .cancel-btn:hover {
    background: #e0e0e0;
  }

  .create-btn {
    background: #2196f3;
    color: white;
  }

  .create-btn:hover:not(:disabled) {
    background: #1976d2;
  }

  .create-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
</style>
