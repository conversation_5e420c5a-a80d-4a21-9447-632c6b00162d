import { MongoClient, Db, Collection } from "mongodb"
import type { Message, Conversation } from "./types"
import { getServerEnv, logEnvVars } from "./env"

// Get environment configuration
const env = getServerEnv()
const MONGODB_URI = env.MONGODB_URI
const DATABASE_NAME = env.MONGODB_DATABASE

// Log environment variables for debugging
logEnvVars()

class MongoDB {
  private client: MongoClient | null = null
  private db: Db | null = null

  async connect(): Promise<void> {
    if (this.client && this.db) {
      return // Already connected
    }

    try {
      this.client = new MongoClient(MONGODB_URI)
      await this.client.connect()
      this.db = this.client.db(DATABASE_NAME)

      console.log(`Connected to MongoDB: ${DATABASE_NAME}`)

      // Create indexes for better performance
      await this.createIndexes()
    } catch (error) {
      console.error("Failed to connect to MongoDB:", error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close()
      this.client = null
      this.db = null
      console.log("Disconnected from MongoDB")
    }
  }

  private async createIndexes(): Promise<void> {
    if (!this.db) return

    try {
      // Conversations indexes
      const conversationsCollection = this.db.collection("conversations")
      await conversationsCollection.createIndex({ sessionId: 1 })
      await conversationsCollection.createIndex({ from: 1 })
      await conversationsCollection.createIndex({ to: 1 })
      await conversationsCollection.createIndex({ updatedAt: -1 })
      await conversationsCollection.createIndex({
        name: "text",
        from: "text",
        to: "text",
        lastMessage: "text",
      })

      // Messages indexes
      const messagesCollection = this.db.collection("messages")
      await messagesCollection.createIndex({ conversationId: 1 })
      await messagesCollection.createIndex({ timestamp: -1 })
      await messagesCollection.createIndex({ conversationId: 1, timestamp: -1 })

      console.log("MongoDB indexes created successfully")
    } catch (error) {
      console.error("Failed to create MongoDB indexes:", error)
    }
  }

  getConversationsCollection(): Collection<Conversation> {
    if (!this.db) {
      throw new Error("MongoDB not connected")
    }
    return this.db.collection<Conversation>("conversations")
  }

  getMessagesCollection(): Collection<Message & { conversationId: string }> {
    if (!this.db) {
      throw new Error("MongoDB not connected")
    }
    return this.db.collection<Message & { conversationId: string }>("messages")
  }

  async ensureConnection(): Promise<void> {
    if (!this.client || !this.db) {
      await this.connect()
    }
  }

  isConnected(): boolean {
    return this.client !== null && this.db !== null
  }
}

console.log("MONGODBURI", MONGODB_URI)
console.log("DATABASENAME", DATABASE_NAME)

// Singleton instance
export const mongodb = new MongoDB()

// Helper functions for database operations
export class ConversationService {
  static async getConversations(
    page = 1,
    perPage = 50,
    search?: string,
  ): Promise<{ items: Conversation[]; total: number }> {
    await mongodb.ensureConnection()
    const collection = mongodb.getConversationsCollection()

    // Build search filter
    const filter: any = {}
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { from: { $regex: search, $options: "i" } },
        { to: { $regex: search, $options: "i" } },
        { lastMessage: { $regex: search, $options: "i" } },
      ]
    }

    // Get total count
    const total = await collection.countDocuments(filter)

    // Get paginated results
    const items = await collection
      .find(filter)
      .sort({ updatedAt: -1 })
      .skip((page - 1) * perPage)
      .limit(perPage)
      .toArray()

    return { items, total }
  }

  static async getConversation(id: string): Promise<Conversation | null> {
    await mongodb.ensureConnection()
    const collection = mongodb.getConversationsCollection()

    return await collection.findOne({
      $or: [
        { _id: id as any },
        { id: id },
        { sessionId: id },
        { from: id },
        { to: id },
      ],
    })
  }

  static async createConversation(
    data: Partial<Conversation>,
  ): Promise<Conversation> {
    await mongodb.ensureConnection()
    const collection = mongodb.getConversationsCollection()

    const conversation: Conversation = {
      id: data.id || Date.now().toString(),
      sessionId: data.sessionId || `session_${Date.now()}`,
      name: data.name || "New Conversation",
      from: data.from || "+1000000000",
      to: data.to || "+1000000001",
      status: data.status || "DISCONNECTED",
      lastMessage: data.lastMessage || undefined,
      timestamp: data.timestamp || new Date(),
      createdAt: data.createdAt || new Date(),
      updatedAt: new Date(),
      messages: [],
    }

    await collection.insertOne(conversation as any)
    return conversation
  }

  static async updateConversation(
    id: string,
    updates: Partial<Conversation>,
  ): Promise<Conversation | null> {
    await mongodb.ensureConnection()
    const collection = mongodb.getConversationsCollection()

    const result = await collection.findOneAndUpdate(
      {
        $or: [{ _id: id as any }, { id: id }, { sessionId: id }],
      },
      {
        $set: {
          ...updates,
          updatedAt: new Date(),
        },
      },
      { returnDocument: "after" },
    )

    return result || null
  }

  static async deleteConversation(id: string): Promise<boolean> {
    await mongodb.ensureConnection()
    const collection = mongodb.getConversationsCollection()
    const messagesCollection = mongodb.getMessagesCollection()

    // Delete conversation
    const result = await collection.deleteOne({
      $or: [{ _id: id as any }, { id: id }, { sessionId: id }],
    })

    // Delete associated messages
    if (result.deletedCount > 0) {
      await messagesCollection.deleteMany({ conversationId: id })
    }

    return result.deletedCount > 0
  }

  static async findOrCreateConversation(
    identifier: string,
    data?: Partial<Conversation>,
  ): Promise<Conversation> {
    let conversation = await this.getConversation(identifier)

    if (!conversation && data) {
      conversation = await this.createConversation({
        ...data,
        id: identifier,
        sessionId: data.sessionId || identifier,
      })
    }

    if (!conversation) {
      throw new Error(`Conversation not found: ${identifier}`)
    }

    return conversation
  }
}

export class MessageService {
  static async getMessages(
    conversationId: string,
    limit = 50,
    offset = 0,
  ): Promise<Message[]> {
    await mongodb.ensureConnection()
    const collection = mongodb.getMessagesCollection()

    return await collection
      .find({ conversationId })
      .sort({ timestamp: -1 })
      .skip(offset)
      .limit(limit)
      .toArray()
  }

  static async createMessage(
    conversationId: string,
    messageData: Omit<Message, "id">,
  ): Promise<Message> {
    await mongodb.ensureConnection()
    const collection = mongodb.getMessagesCollection()

    const message: Message & { conversationId: string } = {
      id: Date.now().toString(),
      ...messageData,
      conversationId,
      timestamp: messageData.timestamp || new Date(),
    }

    await collection.insertOne(message)

    // Update conversation's last message and timestamp
    await ConversationService.updateConversation(conversationId, {
      lastMessage: message.text,
      timestamp: message.timestamp,
      updatedAt: new Date(),
    })

    return message
  }

  static async getMessageCount(conversationId: string): Promise<number> {
    await mongodb.ensureConnection()
    const collection = mongodb.getMessagesCollection()

    return await collection.countDocuments({ conversationId })
  }
}

mongodb.connect().catch(console.error)
