{"name": "chat-client", "version": "1.0.0", "description": "Simple chat client built with Svelte", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.20.4", "svelte": "^4.0.5", "svelte-check": "^3.4.3", "typescript": "^5.0.0", "vite": "^4.4.2"}, "dependencies": {"lucide-svelte": "^0.263.1"}}