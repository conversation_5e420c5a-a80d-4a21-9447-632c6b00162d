"use client"

import { useState, useRef } from "react"
import { useLocalization } from "@/localization/functions/client"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { CheckCircle } from "lucide-react"

import Pagination from "./Pagination"
import { locales } from "./locales"
import { DEFAULT_PER_PAGE } from "./default_per_page"
import { set } from "date-fns"

// Types for bulk table
export interface BulkTableRowData {
  id: string
  columns: any[]
}

export interface BulkTableComponentProps {
  // Table structure
  headers: string[]
  data: BulkTableRowData[]

  // Selection functionality
  selectedIds: string[]
  onSelectAll: (checked: boolean) => void
  onRowSelect: (id: string, checked: boolean) => void

  // Display options
  isScroll?: boolean
  columnWidth?: Record<string, string>
  defaultColumnWidth?: string
  pinnedColumns?: string[]

  // Pagination
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
  perPage?: number

  // Loading state
  isLoading?: boolean

  // Selection info
  showSelectionInfo?: boolean
  selectionInfoText?: string
}

export default function BulkTableComponent({
  headers,
  data,
  selectedIds,
  onSelectAll,
  onRowSelect,
  isScroll = true,
  columnWidth = {},
  defaultColumnWidth = "200px",
  pinnedColumns = [],
  currentPage = 1,
  totalPages = 1,
  onPageChange = () => {},
  perPage = DEFAULT_PER_PAGE,
  isLoading = false,
  showSelectionInfo = true,
  selectionInfoText,
}: BulkTableComponentProps) {
  const { t } = useLocalization("crud-page", locales)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const tableData = data || []

  // Selection logic
  const isAllSelected =
    tableData.length > 0 && selectedIds.length === tableData.length
  const isIndeterminate =
    selectedIds.length > 0 && selectedIds.length < tableData.length

  const handleSelectAll = (checked: boolean) => {
    onSelectAll(checked)
  }

  const handleRowSelect = (id: string, checked: boolean) => {
    onRowSelect(id, checked)
  }

  // Generate shimmer rows when loading
  const renderShimmerRows = () => {
    return Array.from({ length: 3 }, (_, index) => (
      <tr key={`shimmer-${index}`} className="border-t">
        <td className="px-3 py-2">
          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
        </td>
        {headers.map((_, cellIndex) => (
          <td key={cellIndex} className="px-3 py-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
          </td>
        ))}
      </tr>
    ))
  }

  // Drag functionality for horizontal scrolling
  const startDrag = (event: React.MouseEvent) => {
    setIsDragging(true)
    setStartX(event.pageX - (scrollContainerRef.current?.scrollLeft || 0))
  }

  const stopDrag = () => {
    setIsDragging(false)
  }

  const drag = (event: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return
    scrollContainerRef.current.scrollLeft = startX - event.pageX
  }

  return (
    <div className="w-full space-y-4">
      {/* Selection Summary */}
      {showSelectionInfo && selectedIds.length > 0 && (
        <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              {selectionInfoText ||
                `${selectedIds.length} ${t("pagination_of")} ${tableData.length} ${t("bulk_selected")}`}
            </span>
          </div>
          <Badge variant="secondary">
            {selectedIds.length} {t("bulk_selected")}
          </Badge>
        </div>
      )}

      {/* Table Container */}
      <div
        className={`w-full h-full flex flex-col items-center ${isScroll ? "overflow-y-hidden" : ""}`}
      >
        <div
          className={`relative w-full rounded-lg h-[500px] bg-[#F5F5F5] text-xs text-black ${isScroll ? "overflow-y-auto" : ""}`}
          ref={scrollContainerRef}
          onMouseDown={startDrag}
          onMouseUp={stopDrag}
          onMouseLeave={stopDrag}
          onMouseMove={drag}
        >
          <table
            className={`min-w-full max-w-full ${isScroll ? "absolute" : ""}`}
          >
            {/* Table Header with Select All */}
            <thead className="bg-gray-100 sticky top-0 z-10">
              <tr>
                <th className="px-3 py-2 text-left">
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all rows"
                    className={
                      isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""
                    }
                  />
                </th>
                {headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-3 py-2 text-left font-medium text-gray-700"
                    style={{
                      width: columnWidth[header] || defaultColumnWidth,
                      minWidth: columnWidth[header] || defaultColumnWidth,
                    }}
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>

            {/* Table Body */}
            <tbody className="bg-white">
              {isLoading ? (
                renderShimmerRows()
              ) : tableData.length > 0 ? (
                tableData.map((item, index) => {
                  const isSelected = selectedIds.includes(item.id)
                  return (
                    <tr
                      key={`bulk-row-${item.id}-${index}`}
                      className={`border-t hover:bg-gray-50 ${isSelected ? "bg-blue-50" : ""}`}
                    >
                      <td className="px-3 py-2">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => {
                            handleRowSelect(item.id, checked as boolean)
                          }}
                          aria-label={`Select row ${index + 1}`}
                        />
                      </td>
                      {item.columns.map((cell, cellIndex) => (
                        <td
                          key={cellIndex}
                          className="px-3 py-2 text-gray-900"
                          style={{
                            width:
                              columnWidth[headers[cellIndex]] ||
                              defaultColumnWidth,
                            minWidth:
                              columnWidth[headers[cellIndex]] ||
                              defaultColumnWidth,
                          }}
                        >
                          {cell}
                        </td>
                      ))}
                    </tr>
                  )
                })
              ) : (
                <tr>
                  <td
                    colSpan={headers.length + 1}
                    className="text-center py-8 text-gray-500"
                  >
                    {t("table_no_data")}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={onPageChange}
          />
        )}
      </div>
    </div>
  )
}
