"use client"

import { useState } from "react"
import { Edit, Trash2 } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import {
  DisplayVariant,
  CustomRowCardComponent,
} from "./hooks/useDataPageLogic"
import { ActionConfig } from "./types"
import Pagination from "./Pagination"

interface GridViewProps<T extends { id: string } = any> {
  data: T[]
  variant: DisplayVariant
  action?: ActionConfig
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  customRowCard?: CustomRowCardComponent<T>
  isLoading?: boolean
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
  perPage?: number
}

// Default card component for grid/card views
function DefaultRowCard<T extends { id: string }>({
  item,
  variant,
  onEdit,
  onDelete,
  action,
}: {
  item: T
  variant: DisplayVariant
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  action?: ActionConfig
}) {
  const { t } = useLocalization("crud-page", locales)

  const cardClass =
    variant === "GRID"
      ? "bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
      : "bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"

  const renderItemData = () => {
    const entries = Object.entries(item).filter(([key]) => key !== "id")

    if (variant === "GRID") {
      return (
        <div className="space-y-2">
          {entries.slice(0, 3).map(([key, value]) => (
            <div key={key} className="text-sm">
              <span className="font-medium text-gray-600 capitalize">
                {key.replace(/_/g, " ")}:
              </span>
              <span className="ml-2 text-gray-900">
                {typeof value === "string" ? value : JSON.stringify(value)}
              </span>
            </div>
          ))}
          {entries.length > 3 && (
            <div className="text-xs text-gray-500">
              {t("and")} {entries.length - 3} {t("more_fields")}
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {entries.map(([key, value]) => (
          <div key={key} className="flex flex-col">
            <span className="text-sm font-medium text-gray-600 capitalize">
              {key.replace(/_/g, " ")}
            </span>
            <span className="text-gray-900 mt-1">
              {typeof value === "string" ? value : JSON.stringify(value)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={cardClass}>
      {renderItemData()}

      {(action?.edit || action?.delete) && (
        <div className="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-100">
          {action?.edit && onEdit && (
            <button
              onClick={() => onEdit(item.id)}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
              title={t("edit_button")}
            >
              <Edit className="w-4 h-4 mr-1" />
              {t("edit_button")}
            </button>
          )}
          {action?.delete && onDelete && (
            <button
              onClick={() => onDelete(item.id)}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
              title={t("delete_button")}
            >
              <Trash2 className="w-4 h-4 mr-1" />
              {t("delete_button")}
            </button>
          )}
        </div>
      )}
    </div>
  )
}

// Shimmer loading component for grid/card views
function ShimmerCard({ variant }: { variant: DisplayVariant }) {
  const cardClass =
    variant === "GRID"
      ? "bg-white rounded-lg shadow-sm border border-gray-200 p-4"
      : "bg-white rounded-lg shadow-sm border border-gray-200 p-6"

  return (
    <div className={cardClass}>
      <div className="animate-pulse space-y-3">
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        {variant === "CARD" && (
          <>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </>
        )}
      </div>
    </div>
  )
}

export default function GridView<T extends { id: string }>({
  data,
  variant,
  action,
  onEdit,
  onDelete,
  customRowCard,
  isLoading = false,
  currentPage = 1,
  totalPages = 1,
  onPageChange = () => {},
  perPage = 10,
}: GridViewProps<T>) {
  const { t } = useLocalization("crud-page", locales)

  const gridClass =
    variant === "GRID"
      ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
      : "grid grid-cols-1 lg:grid-cols-2 gap-6"

  const renderShimmerCards = () => {
    const count = variant === "GRID" ? 8 : 4
    return Array.from({ length: count }, (_, index) => (
      <ShimmerCard key={`shimmer-${index}`} variant={variant} />
    ))
  }

  const renderCard = (item: T, index: number) => {
    if (customRowCard) {
      const CustomComponent = customRowCard.component
      return (
        <CustomComponent
          key={item.id || index}
          item={item}
          variant={variant}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      )
    }

    return (
      <DefaultRowCard
        key={item.id || index}
        item={item}
        variant={variant}
        action={action}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    )
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className={gridClass}>
          {isLoading ? (
            renderShimmerCards()
          ) : data.length > 0 ? (
            data.map(renderCard)
          ) : (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-500 text-lg">{t("table_no_data")}</div>
            </div>
          )}
        </div>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
      />
    </div>
  )
}
