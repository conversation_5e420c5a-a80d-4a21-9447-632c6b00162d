"use client"

import { useState } from "react"
import { ChevronDown, Table, Grid3X3, LayoutGrid } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DisplayVariant } from "./hooks/useDataPageLogic"

interface ViewSwitcherProps {
  currentVariant: DisplayVariant
  onVariantChange: (variant: DisplayVariant) => void
  allowViewSwitching?: boolean
}

export default function ViewSwitcher({
  currentVariant,
  onVariantChange,
  allowViewSwitching = true,
}: ViewSwitcherProps) {
  const { t } = useLocalization("crud-page", locales)
  const [open, setOpen] = useState(false)

  if (!allowViewSwitching) return null

  const variants = [
    {
      value: "TABLE" as DisplayVariant,
      icon: Table,
      label: t("table_view"),
      description: t("table_view_description"),
    },
    {
      value: "GRID" as DisplayVariant,
      icon: Grid3X3,
      label: t("grid_view"),
      description: t("grid_view_description"),
    },
    {
      value: "CARD" as DisplayVariant,
      icon: LayoutGrid,
      label: t("card_view"),
      description: t("card_view_description"),
    },
  ]

  const current = variants.find((v) => v.value === currentVariant)

  return (
    <div className="relative inline-block text-left">
      <button
        onClick={() => setOpen((prev) => !prev)}
        className="inline-flex items-center px-3 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none"
      >
        {current?.icon && <current.icon className="w-4 h-4 mr-2" />}
        {current?.label}
        <ChevronDown className="w-4 h-4 ml-2" />
      </button>

      {open && (
        <div
          className="absolute z-40 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          role="menu"
        >
          <div className="py-1">
            {variants.map(({ value, icon: Icon, label, description }) => (
              <button
                key={value}
                onClick={() => {
                  onVariantChange(value)
                  setOpen(false)
                }}
                className={`flex w-full items-center px-4 py-2 text-sm text-left hover:bg-gray-100 ${
                  currentVariant === value ? "bg-gray-100 font-medium" : ""
                }`}
                title={description}
              >
                <Icon className="w-4 h-4 mr-2 text-gray-500" />
                {label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
