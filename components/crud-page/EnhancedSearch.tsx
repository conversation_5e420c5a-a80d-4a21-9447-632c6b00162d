"use client"

import { useState, useEffect } from "react"
import { Search, Filter, X, Plus, Save, RotateCcw } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { MultipleFilter, MultipleSort } from "./types"

interface SearchFacet {
  id: string
  field: string
  label: string
  type: "text" | "select" | "date" | "number" | "multiselect"
  options?: { value: string; label: string }[]
  placeholder?: string
}

interface EnhancedSearchProps {
  searchConfig?: {
    facets?: SearchFacet[]
    quickFilters?: { label: string; filters: MultipleFilter[] }[]
    allowSaveSearch?: boolean
  }
  currentFilters: {
    search: string
    dateFilter?: string
    sort?: { field: string; direction: "ASC" | "DESC" }
    filters?: { field: string; operator?: string; value: any }[]
    sorts?: { field: string; direction: "ASC" | "DESC" }[]
  }
  onFiltersChange: (
    search: string,
    dateFilter?: string,
    sort?: { field: string; direction: "ASC" | "DESC" },
    filters?: { field: string; operator?: string; value: any }[],
    sorts?: { field: string; direction: "ASC" | "DESC" }[]
  ) => void
}

export default function EnhancedSearch({
  searchConfig,
  currentFilters,
  onFiltersChange,
}: EnhancedSearchProps) {
  const { t } = useLocalization("crud-page", locales)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [searchText, setSearchText] = useState(currentFilters.search)
  const [activeFacets, setActiveFacets] = useState<MultipleFilter[]>([])
  const [savedSearches, setSavedSearches] = useState<
    { id: string; name: string; filters: MultipleFilter[]; search: string }[]
  >([])

  // Load saved searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem("enhanced-search-saved")
    if (saved) {
      try {
        setSavedSearches(JSON.parse(saved))
      } catch (error) {
        console.warn("Failed to parse saved searches")
      }
    }
  }, [])

  // Generate unique ID for filters
  const generateId = () => Math.random().toString(36).substring(2, 11)

  const handleSearchChange = (value: string) => {
    setSearchText(value)
    onFiltersChange(
      value,
      currentFilters.dateFilter,
      currentFilters.sort,
      activeFacets.filter(f => f.enabled && f.value),
      currentFilters.sorts
    )
  }

  const handleAddFacet = (facet: SearchFacet) => {
    const newFilter: MultipleFilter = {
      id: generateId(),
      enabled: true,
      field: facet.field,
      operator: facet.type === "text" ? "contains" : "equals",
      value: "",
    }
    setActiveFacets(prev => [...prev, newFilter])
  }

  const handleUpdateFacet = (
    filterId: string,
    field: keyof MultipleFilter,
    value: any
  ) => {
    setActiveFacets(prev =>
      prev.map(filter =>
        filter.id === filterId ? { ...filter, [field]: value } : filter
      )
    )
  }

  const handleRemoveFacet = (filterId: string) => {
    setActiveFacets(prev => prev.filter(f => f.id !== filterId))
  }

  const handleApplyFilters = () => {
    onFiltersChange(
      searchText,
      currentFilters.dateFilter,
      currentFilters.sort,
      activeFacets.filter(f => f.enabled && f.value),
      currentFilters.sorts
    )
  }

  const handleClearAll = () => {
    setSearchText("")
    setActiveFacets([])
    onFiltersChange("", undefined, undefined, [], undefined)
  }

  const handleSaveSearch = () => {
    const name = prompt(t("save_search"))
    if (name) {
      const newSavedSearch = {
        id: generateId(),
        name,
        filters: activeFacets,
        search: searchText,
      }
      const updated = [...savedSearches, newSavedSearch]
      setSavedSearches(updated)
      localStorage.setItem("enhanced-search-saved", JSON.stringify(updated))
    }
  }

  const handleLoadSavedSearch = (saved: typeof savedSearches[0]) => {
    setSearchText(saved.search)
    setActiveFacets(saved.filters)
    onFiltersChange(
      saved.search,
      currentFilters.dateFilter,
      currentFilters.sort,
      saved.filters.filter(f => f.enabled && f.value),
      currentFilters.sorts
    )
  }

  const renderFacetInput = (facet: SearchFacet, filter: MultipleFilter) => {
    const facetConfig = searchConfig?.facets?.find(f => f.field === filter.field)
    if (!facetConfig) return null

    switch (facetConfig.type) {
      case "select":
        return (
          <select
            value={filter.value}
            onChange={(e) => handleUpdateFacet(filter.id, "value", e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{t("select_value")}</option>
            {facetConfig.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )

      case "multiselect":
        return (
          <select
            multiple
            value={Array.isArray(filter.value) ? filter.value : []}
            onChange={(e) => {
              const values = Array.from(e.target.selectedOptions, option => option.value)
              handleUpdateFacet(filter.id, "value", values)
            }}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px]"
          >
            {facetConfig.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )

      case "date":
        return (
          <input
            type="date"
            value={filter.value}
            onChange={(e) => handleUpdateFacet(filter.id, "value", e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        )

      case "number":
        return (
          <input
            type="number"
            value={filter.value}
            onChange={(e) => handleUpdateFacet(filter.id, "value", e.target.value)}
            placeholder={facetConfig.placeholder || t("enter_value")}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        )

      default:
        return (
          <input
            type="text"
            value={filter.value}
            onChange={(e) => handleUpdateFacet(filter.id, "value", e.target.value)}
            placeholder={facetConfig.placeholder || t("enter_value")}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        )
    }
  }

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            value={searchText}
            onChange={(e) => handleSearchChange(e.target.value)}
            placeholder={t("search_placeholder")}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        {searchConfig?.facets && (
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`px-4 py-2 rounded-lg border transition-colors ${
              showAdvanced
                ? "bg-blue-50 border-blue-200 text-blue-700"
                : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
            }`}
          >
            <Filter className="w-4 h-4 mr-2 inline" />
            {t("advanced_search")}
          </button>
        )}
      </div>

      {/* Quick Filters */}
      {searchConfig?.quickFilters && searchConfig.quickFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600 font-medium">{t("quick_filters")}:</span>
          {searchConfig.quickFilters.map((quickFilter, index) => (
            <button
              key={index}
              onClick={() => {
                setActiveFacets(quickFilter.filters)
                handleApplyFilters()
              }}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
            >
              {quickFilter.label}
            </button>
          ))}
        </div>
      )}

      {/* Advanced Search Panel */}
      {showAdvanced && searchConfig?.facets && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">{t("search_facets")}</h3>
            <div className="flex items-center space-x-2">
              {searchConfig.allowSaveSearch && (
                <button
                  onClick={handleSaveSearch}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Save className="w-4 h-4 mr-1 inline" />
                  {t("save_search")}
                </button>
              )}
              <button
                onClick={handleClearAll}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                <RotateCcw className="w-4 h-4 mr-1 inline" />
                {t("clear_all_filters")}
              </button>
            </div>
          </div>

          {/* Available Facets */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600 font-medium">{t("filter_by")}:</span>
            {searchConfig.facets.map(facet => (
              <button
                key={facet.id}
                onClick={() => handleAddFacet(facet)}
                className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                <Plus className="w-3 h-3 mr-1 inline" />
                {facet.label}
              </button>
            ))}
          </div>

          {/* Active Facets */}
          {activeFacets.length > 0 && (
            <div className="space-y-3">
              {activeFacets.map(filter => {
                const facet = searchConfig.facets?.find(f => f.field === filter.field)
                if (!facet) return null

                return (
                  <div key={filter.id} className="flex items-center space-x-3 bg-white p-3 rounded-md border">
                    <span className="text-sm font-medium text-gray-700 min-w-[100px]">
                      {facet.label}:
                    </span>
                    {renderFacetInput(facet, filter)}
                    <button
                      onClick={() => handleRemoveFacet(filter.id)}
                      className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )
              })}
            </div>
          )}

          {/* Saved Searches */}
          {savedSearches.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">{t("saved_searches")}:</h4>
              <div className="flex flex-wrap gap-2">
                {savedSearches.map(saved => (
                  <button
                    key={saved.id}
                    onClick={() => handleLoadSavedSearch(saved)}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    {saved.name}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
