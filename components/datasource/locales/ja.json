{"form": {"buttons": {"cancel": "キャンセル", "save": "保存", "saving": "保存中..."}, "common": {"approaching_limit": "上限に近づいています", "characters": "文字"}, "fields": {"access_key": {"label": "アクセスキー（任意）", "placeholder": "必要な場合はアクセスキーを入力"}, "content": {"label": "コンテンツ", "placeholder": "ここにテキストを入力..."}, "name": {"label": "名称", "placeholder": "データソース名を入力"}, "type": {"api": "API エンドポイント", "database": "データベース", "label": "種類", "placeholder": "種類を選択", "text": "テキストコンテンツ"}, "url": {"label": "URL", "placeholder": "https://example.com/api"}}, "section": {"connection_desc": "このデータソースの接続情報を入力してください", "connection_title": "接続情報", "content_desc": "このデータソースのテキストコンテンツを入力してください", "content_title": "コンテンツ", "details_desc": "データソースの基本情報を入力してください", "details_title": "データソースの詳細"}, "validation": {"content_required": "TEXT タイプではコンテンツを入力してください", "name_required": "名前を入力してください", "save_failed": "データソースの保存に失敗しました。もう一度お試しください。", "url_required": "TEXT 以外のタイプでは URL を入力してください"}}, "view": {"additional": {"created_at": "作成日時", "created_by": "作成者", "desc": "タイムスタンプとメタデータ", "title": "追加情報", "updated_at": "更新日時", "updated_by": "更新者"}, "basic": {"created": "作成日", "desc": "データソースの詳細とメタデータ", "name": "名称", "status": "ステータス", "title": "基本情報", "type": "種類"}, "buttons": {"delete": "削除", "edit": "編集"}, "connection": {"access_key": "アクセスキー", "desc": "このデータソースの接続情報", "no_url": "URL がありません", "title": "接続情報", "url": "URL"}, "content": {"desc": "このデータソースに保存されているテキストコンテンツ", "label": "コンテンツ", "no_content": "コンテンツがありません", "title": "コンテンツ"}, "header": {"subtitle": "データソースの詳細を表示"}, "status": {"active": "有効", "inactive": "無効"}}}