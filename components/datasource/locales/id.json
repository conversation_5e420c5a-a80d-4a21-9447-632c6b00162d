{"form": {"buttons": {"cancel": "<PERSON><PERSON>", "save": "Simpan", "saving": "Menyimpan..."}, "common": {"approaching_limit": "Mendekati batas", "characters": "karakter"}, "fields": {"access_key": {"label": "Access Key (Opsional)", "placeholder": "Masukkan access key jika dip<PERSON><PERSON>an"}, "content": {"label": "Konten", "placeholder": "<PERSON><PERSON><PERSON><PERSON> konten teks di sini..."}, "name": {"label": "<PERSON><PERSON>", "placeholder": "Masukkan nama datasource"}, "type": {"api": "Endpoint API", "database": "Database", "label": "Tipe", "placeholder": "<PERSON><PERSON><PERSON> tipe", "text": "Konten Teks"}, "url": {"label": "URL", "placeholder": "https://contoh.com/api"}}, "section": {"connection_desc": "Masukkan detail koneksi untuk datasource ini", "connection_title": "Detail <PERSON>i", "content_desc": "Masukkan konten teks untuk datasource ini", "content_title": "Konten", "details_desc": "Masukkan informasi dasar untuk datasource Anda", "details_title": "Detail Datasource"}, "validation": {"content_required": "Konten harus diisi untuk tipe TEXT", "name_required": "<PERSON><PERSON> harus diisi", "save_failed": "Gagal menyimpan datasource. Coba lagi.", "url_required": "URL harus diisi untuk tipe non-TEXT"}}, "view": {"additional": {"created_at": "Dibuat Pada", "created_by": "Dibuat Oleh", "desc": "Tanda waktu dan metadata", "title": "Informasi <PERSON>", "updated_at": "<PERSON><PERSON><PERSON><PERSON>", "updated_by": "<PERSON><PERSON><PERSON><PERSON>"}, "basic": {"created": "Dibuat", "desc": "Detail dan metadata datasource", "name": "<PERSON><PERSON>", "status": "Status", "title": "Informasi <PERSON>", "type": "Tipe"}, "buttons": {"delete": "Hapus", "edit": "Ubah"}, "connection": {"access_key": "Access Key", "desc": "Informasi koneksi untuk datasource ini", "no_url": "Tidak ada URL", "title": "Detail <PERSON>i", "url": "URL"}, "content": {"desc": "Konten teks yang disimpan pada datasource ini", "label": "Konten", "no_content": "Tidak ada konten", "title": "Konten"}, "header": {"subtitle": "Lihat detail datasource"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON><PERSON>"}}}