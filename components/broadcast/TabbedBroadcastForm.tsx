"use client"

import { Broadcast } from "@/lib/repositories/broadcast"
import { Device } from "@/lib/repositories/devices/interface"
import { BroadcastRecipientsAPI, DevicesAPI } from "@/lib/services"
import type {
  BroadcastRecipientContact,
  TagWithCount,
} from "@/lib/services/broadcastRecipientsApi"
import { enhancedBroadcastFormLocales } from "@/localization/enhanced-broadcast-form"
import { useLocalization } from "@/localization/functions/client"
import {
  ArrowLeft,
  MessageSquare,
  Send,
  Users
} from "lucide-react"
import { useEffect, useState } from "react"
import { Button } from "../ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs"
import { RecipientSelector } from "./broadcast-form/RecipientSelector"
import { BroadcastInfoForm } from "./broadcast-form/BroadcastInfoForm"

interface TabbedBroadcastFormProps {
  initialBroadcast?: Broadcast | null
  onSave: (data: {
    title: string
    message: string
    deviceId: string
    recipientTags?: string[]
    excludedRecipientIds?: string[]
  }) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
  submitButtonText?: string
  title: string
  description: string
}

export default function TabbedBroadcastForm({
  initialBroadcast = null,
  onSave,
  onCancel,
  isSubmitting = false,
  submitButtonText,
  title,
  description,
}: TabbedBroadcastFormProps) {
  const { t } = useLocalization(
    "enhanced-broadcast-form",
    enhancedBroadcastFormLocales,
  )

  // Form state
  const [broadcastTitle, setBroadcastTitle] = useState(
    initialBroadcast?.title || "",
  )
  const [message, setMessage] = useState(initialBroadcast?.message || "")
  const [selectedDeviceId, setSelectedDeviceId] = useState(
    initialBroadcast?.deviceId || "",
  )

  // Recipient selection state
  const [selectedTags, setSelectedTags] = useState<string[]>(
    initialBroadcast?.recipientTags || [],
  )
  const [excludedContactIds, setExcludedContactIds] = useState<string[]>(
    initialBroadcast?.excludedRecipientIds || [],
  )
  const [availableTags, setAvailableTags] = useState<TagWithCount[]>([])
  const [contacts, setContacts] = useState<BroadcastRecipientContact[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalRecipients, setTotalRecipients] = useState(0)
  const [loading, setLoading] = useState(false)

  // Device state
  const [devices, setDevices] = useState<Device[]>([])
  const [loadingDevices, setLoadingDevices] = useState(false)

  // Load devices
  useEffect(() => {
    const loadDevices = async () => {
      setLoadingDevices(true)
      try {
        const response = await DevicesAPI.All({
          page: 1,
          per_page: 100,
        }).request()
        setDevices(response.items || [])
      } catch (error) {
        console.error("Failed to load devices:", error)
      } finally {
        setLoadingDevices(false)
      }
    }
    loadDevices()
  }, [])

  // Load available tags and initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const response =
          await BroadcastRecipientsAPI.GetAvailableTags().request()
        setAvailableTags(response.availableTags || [])
      } catch (error) {
        console.error("Failed to load available tags:", error)
      }
    }
    loadInitialData()
  }, [])

  // Load contacts when filters change
  useEffect(() => {
    const loadContacts = async () => {
      setLoading(true)
      try {
        const response = await BroadcastRecipientsAPI.All({
          page: currentPage,
          per_page: 20,
          search: searchTerm || undefined,
          // tags: selectedTags.length > 0 ? selectedTags : undefined,
          // excludedIds: excludedContactIds.length > 0 ? excludedContactIds : undefined,
        }).request()

        setContacts(response.contacts || [])
        setTotalRecipients(response.selectedCount || 0)
      } catch (error) {
        console.error("Failed to load contacts:", error)
        setContacts([])
        setTotalRecipients(0)
      } finally {
        setLoading(false)
      }
    }

    loadContacts()
  }, [selectedTags, excludedContactIds, searchTerm, currentPage])

  const handleTagToggle = (tagName: string) => {
    const newTags = selectedTags.includes(tagName)
      ? selectedTags.filter((t) => t !== tagName)
      : [...selectedTags, tagName]
    setSelectedTags(newTags)
    setCurrentPage(1)
  }

  const handleContactExclude = (contactId: string) => {
    const newExcluded = excludedContactIds.includes(contactId)
      ? excludedContactIds.filter((id) => id !== contactId)
      : [...excludedContactIds, contactId]
    setExcludedContactIds(newExcluded)
  }

  // Calculate final recipient count
  const calculateRecipientCount = () => {
    if (selectedTags.length === 0) {
      // No tags selected = all contacts minus excluded
      return Math.max(0, totalRecipients - excludedContactIds.length)
    } else {
      // Tags selected = sum of tag counts minus excluded contacts that have those tags
      const taggedContactsCount = availableTags
        .filter((tagObj) => selectedTags.includes(tagObj.tag))
        .reduce((sum, tagObj) => sum + tagObj.count, 0)

      // Count excluded contacts that have selected tags
      const excludedTaggedContacts = contacts.filter(
        (contact) =>
          excludedContactIds.includes(contact.id) &&
          contact.tags.some((tag) => selectedTags.includes(tag)),
      ).length

      return Math.max(0, taggedContactsCount - excludedTaggedContacts)
    }
  }

  const finalRecipientCount = calculateRecipientCount()

  // Check if contact should be included (green)
  const shouldContactBeIncluded = (contact: BroadcastRecipientContact) => {
    // If contact is excluded, it should not be included
    if (excludedContactIds.includes(contact.id)) return false

    if (selectedTags.length === 0) {
      // No tags selected = all contacts should be included
      return true
    } else {
      // Tags selected = only contacts with selected tags should be included
      return contact.tags.some((tag) => selectedTags.includes(tag))
    }
  }

  // Determine button action for contact
  const getContactButtonAction = (contact: BroadcastRecipientContact) => {
    const isExcluded = excludedContactIds.includes(contact.id)

    if (isExcluded) {
      return "include" // Show include button for excluded contacts
    }

    if (selectedTags.length === 0) {
      // No tags selected = all contacts are green, show exclude button
      return "exclude"
    } else {
      // Tags selected
      const hasSelectedTags = contact.tags.some((tag) =>
        selectedTags.includes(tag),
      )
      if (hasSelectedTags) {
        // Contact has selected tags = green, show exclude button
        return "exclude"
      } else {
        // Contact doesn't have selected tags = not green, show include button
        return "include"
      }
    }
  }

  const messageLength = message.length
  const maxLength = 1000

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!broadcastTitle.trim()) {
      alert(t("form.validation.title_required"))
      return
    }

    if (!message.trim()) {
      alert(t("form.validation.message_required"))
      return
    }

    if (!selectedDeviceId) {
      alert(t("form.validation.device_required"))
      return
    }

    await onSave({
      title: broadcastTitle.trim(),
      message: message.trim(),
      deviceId: selectedDeviceId,
      recipientTags: selectedTags.length > 0 ? selectedTags : undefined,
      excludedRecipientIds:
        excludedContactIds.length > 0 ? excludedContactIds : undefined,
    })
  }

  return (
    <div className="w-full h-full px-60 overflow-y-auto">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          aria-label={t("form.buttons.back")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t("form.buttons.back")}
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>
      </div>

      <div className="h-full">
        <Tabs defaultValue="broadcast-info" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="broadcast-info"
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              {t("tabs.broadcast_info")}
            </TabsTrigger>
            <TabsTrigger value="recipients" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              {t("tabs.recipients")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="broadcast-info">
            <BroadcastInfoForm
              broadcastTitle={broadcastTitle}
              message={message}
              selectedDeviceId={selectedDeviceId}
              devices={devices}
              loadingDevices={loadingDevices}
              setBroadcastTitle={setBroadcastTitle}
              setMessage={setMessage}
              setSelectedDeviceId={setSelectedDeviceId}
            />
          </TabsContent>

          <TabsContent value="recipients">
            <RecipientSelector
              selectedTags={selectedTags}
              excludedContactIds={excludedContactIds}
              availableTags={availableTags}
              contacts={contacts}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              handleTagToggle={handleTagToggle}
              handleContactExclude={handleContactExclude}
              loading={loading}
              finalRecipientCount={finalRecipientCount}
              totalRecipients={totalRecipients}
            />
          </TabsContent>

        </Tabs>
      </div>
      {/* Submit Button */}
      <div className="flex justify-end gap-4 pt-6 bottom-0 sticky bg-white border-t border-gray-200">
        <Button type="button" variant="outline" onClick={onCancel}>
          {t("form.buttons.cancel")}
        </Button>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={
            isSubmitting ||
            !broadcastTitle.trim() ||
            !message.trim() ||
            !selectedDeviceId
          }
          className="flex items-center gap-2"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {t("form.buttons.saving")}
            </>
          ) : (
            <>
              <Send className="h-4 w-4" />
              {submitButtonText}
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
