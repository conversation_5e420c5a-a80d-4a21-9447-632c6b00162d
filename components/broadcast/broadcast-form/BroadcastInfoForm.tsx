"use client"

import {
    <PERSON>,
    <PERSON><PERSON>eader,
    CardTitle,
    CardContent,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Smartphone, MessageSquare } from "lucide-react"
import { MobileChatPreview } from "../MobileChatPreview"
import { useLocalization } from "@/localization/functions/client"
import { enhancedBroadcastFormLocales } from "@/localization/enhanced-broadcast-form"
import { Device } from "@/lib/repositories/devices/interface"

interface BroadcastInfoFormProps {
    broadcastTitle: string
    message: string
    selectedDeviceId: string
    devices: Device[]
    loadingDevices: boolean
    setBroadcastTitle: (val: string) => void
    setMessage: (val: string) => void
    setSelectedDeviceId: (val: string) => void
}

export function BroadcastInfoForm({
    broadcastTitle,
    message,
    selectedDeviceId,
    devices,
    loadingDevices,
    setBroadcastTitle,
    setMessage,
    setSelectedDeviceId,
}: BroadcastInfoFormProps) {
    const { t } = useLocalization("enhanced-broadcast-form", enhancedBroadcastFormLocales)

    const messageLength = message.length
    const maxLength = 1000

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Form */}
            <div className="space-y-6">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <MessageSquare className="h-5 w-5" />
                            {t("form.sections.basic_info")}
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <Label htmlFor="title">{t("form.fields.title")}</Label>
                            <Input
                                id="title"
                                value={broadcastTitle}
                                onChange={(e) => setBroadcastTitle(e.target.value)}
                                placeholder={t("form.placeholders.title")}
                                required
                            />
                        </div>

                        <div>
                            <Label htmlFor="message">{t("form.fields.message")}</Label>
                            <Textarea
                                id="message"
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                placeholder={t("form.placeholders.message")}
                                rows={4}
                                maxLength={maxLength}
                                required
                            />
                            <div className="flex justify-between text-sm text-muted-foreground mt-1">
                                <span>{t("form.hints.message_hint")}</span>
                                <span
                                    className={
                                        messageLength > maxLength * 0.9 ? "text-orange-500" : ""
                                    }
                                >
                                    {messageLength}/{maxLength}
                                </span>
                            </div>
                        </div>

                        <div>
                            <Label htmlFor="device">{t("form.fields.device")}</Label>
                            <Select
                                value={selectedDeviceId}
                                onValueChange={setSelectedDeviceId}
                                required
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder={t("form.placeholders.device")} />
                                </SelectTrigger>
                                <SelectContent>
                                    {loadingDevices ? (
                                        <SelectItem value="loading" disabled>
                                            {t("form.loading.devices")}
                                        </SelectItem>
                                    ) : devices.length === 0 ? (
                                        <SelectItem value="empty" disabled>
                                            {t("form.empty.devices")}
                                        </SelectItem>
                                    ) : (
                                        devices.map((device) => (
                                            <SelectItem key={device.id} value={device.id}>
                                                <div className="flex items-center gap-2">
                                                    <Smartphone className="h-4 w-4" />
                                                    {device.name}
                                                </div>
                                            </SelectItem>
                                        ))
                                    )}
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Right Column - Preview */}
            <div className="space-y-6">
                <MobileChatPreview message={message} />
            </div>
        </div>
    )
}
