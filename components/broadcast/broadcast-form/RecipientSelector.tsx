"use client"

import { useState, useEffect } from "react"
import {
    Users,
    X,
    Check,
    Search,
    Tag,
} from "lucide-react"
import {
    <PERSON>,
    CardHeader,
    CardTitle,
    CardDescription,
    CardContent,
} from "../ui/card"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { BroadcastRecipientsAPI } from "@/lib/services"
import type {
    TagWithCount,
    BroadcastRecipientContact,
} from "@/lib/services/broadcastRecipientsApi"
import { useLocalization } from "@/localization/functions/client"
import { enhancedBroadcastFormLocales } from "@/localization/enhanced-broadcast-form"

interface RecipientSelectorProps {
    selectedTags: string[]
    onSelectedTagsChange: (tags: string[]) => void
    excludedContactIds: string[]
    onExcludedContactIdsChange: (ids: string[]) => void
}

export function RecipientSelector({
    selectedTags,
    onSelectedTagsChange,
    excludedContactIds,
    onExcludedContactIdsChange,
}: RecipientSelectorProps) {
    const { t } = useLocalization(
        "enhanced-broadcast-form",
        enhancedBroadcastFormLocales,
    )

    const [availableTags, setAvailableTags] = useState<TagWithCount[]>([])
    const [contacts, setContacts] = useState<BroadcastRecipientContact[]>([])
    const [searchTerm, setSearchTerm] = useState("")
    const [currentPage, setCurrentPage] = useState(1)
    const [totalRecipients, setTotalRecipients] = useState(0)
    const [loading, setLoading] = useState(false)

    // Load available tags once
    useEffect(() => {
        const loadAvailableTags = async () => {
            try {
                const response =
                    await BroadcastRecipientsAPI.GetAvailableTags().request()
                setAvailableTags(response.availableTags || [])
            } catch (error) {
                console.error("Failed to load available tags:", error)
            }
        }
        loadAvailableTags()
    }, [])

    // Load contacts whenever filters change
    useEffect(() => {
        const loadContacts = async () => {
            setLoading(true)
            try {
                const response = await BroadcastRecipientsAPI.All({
                    page: currentPage,
                    per_page: 20,
                    search: searchTerm || undefined,
                    // Uncomment these if the API supports filtering by tags and excluded IDs
                    // tags: selectedTags.length > 0 ? selectedTags : undefined,
                    // excludedIds: excludedContactIds.length > 0 ? excludedContactIds : undefined,
                }).request()

                setContacts(response.contacts || [])
                setTotalRecipients(response.selectedCount || 0)
            } catch (error) {
                console.error("Failed to load contacts:", error)
                setContacts([])
                setTotalRecipients(0)
            } finally {
                setLoading(false)
            }
        }
        loadContacts()
    }, [selectedTags, excludedContactIds, searchTerm, currentPage])

    // Tag toggle handler
    const handleTagToggle = (tagName: string) => {
        const newTags = selectedTags.includes(tagName)
            ? selectedTags.filter((t) => t !== tagName)
            : [...selectedTags, tagName]
        onSelectedTagsChange(newTags)
        setCurrentPage(1)
    }

    // Exclude/include contact handler
    const handleContactExclude = (contactId: string) => {
        const newExcluded = excludedContactIds.includes(contactId)
            ? excludedContactIds.filter((id) => id !== contactId)
            : [...excludedContactIds, contactId]
        onExcludedContactIdsChange(newExcluded)
    }

    // Calculate final recipient count logic
    const calculateRecipientCount = () => {
        if (selectedTags.length === 0) {
            return Math.max(0, totalRecipients - excludedContactIds.length)
        } else {
            const taggedContactsCount = availableTags
                .filter((tagObj) => selectedTags.includes(tagObj.tag))
                .reduce((sum, tagObj) => sum + tagObj.count, 0)

            const excludedTaggedContacts = contacts.filter(
                (contact) =>
                    excludedContactIds.includes(contact.id) &&
                    contact.tags.some((tag) => selectedTags.includes(tag)),
            ).length

            return Math.max(0, taggedContactsCount - excludedTaggedContacts)
        }
    }

    const finalRecipientCount = calculateRecipientCount()

    // Determine if contact is included (green)
    const shouldContactBeIncluded = (contact: BroadcastRecipientContact) => {
        if (excludedContactIds.includes(contact.id)) return false

        if (selectedTags.length === 0) {
            return true
        } else {
            return contact.tags.some((tag) => selectedTags.includes(tag))
        }
    }

    // Determine contact button action (include or exclude)
    const getContactButtonAction = (contact: BroadcastRecipientContact) => {
        const isExcluded = excludedContactIds.includes(contact.id)

        if (isExcluded) {
            return "include"
        }

        if (selectedTags.length === 0) {
            return "exclude"
        } else {
            const hasSelectedTags = contact.tags.some((tag) =>
                selectedTags.includes(tag),
            )
            return hasSelectedTags ? "exclude" : "include"
        }
    }

    return (
        <>
            {/* Recipient Summary */}
            <Card>
                <CardContent className="pt-6">
                    <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="font-medium text-blue-900">
                                    {t("form.recipient_summary.final_count", {
                                        count: finalRecipientCount,
                                    })}
                                </p>
                                {selectedTags.length === 0 ? (
                                    <p className="text-sm text-blue-700">
                                        {t("form.recipient_summary.all_contacts", {
                                            count: totalRecipients,
                                        })}
                                    </p>
                                ) : (
                                    <p className="text-sm text-blue-700">
                                        {t("form.recipient_summary.tagged_contacts", {
                                            count: availableTags
                                                .filter((tagObj) =>
                                                    selectedTags.includes(tagObj.tag),
                                                )
                                                .reduce((sum, tagObj) => sum + tagObj.count, 0),
                                        })}
                                    </p>
                                )}
                                {selectedTags.length > 0 && (
                                    <p className="text-sm text-blue-600">
                                        {t("form.recipient_summary.selected_tags", {
                                            tags: selectedTags.join(", "),
                                        })}
                                    </p>
                                )}
                                {excludedContactIds.length > 0 && (
                                    <p className="text-sm text-red-600">
                                        {t("form.recipient_summary.excluding_contacts", {
                                            count:
                                                selectedTags.length === 0
                                                    ? excludedContactIds.length
                                                    : contacts.filter(
                                                        (contact) =>
                                                            excludedContactIds.includes(contact.id) &&
                                                            contact.tags.some((tag) =>
                                                                selectedTags.includes(tag),
                                                            ),
                                                    ).length,
                                        })}
                                    </p>
                                )}
                            </div>
                            <Badge variant="default" className="text-lg px-3 py-1">
                                {finalRecipientCount}
                            </Badge>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Tag Selection */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Tag className="h-5 w-5" />
                        {t("form.sections.tag_selection")}
                    </CardTitle>
                    <CardDescription>{t("form.hints.tag_selection")}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label>{t("form.fields.tags")}</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                            {availableTags.map((tagObj) => (
                                <Badge
                                    key={tagObj.tag}
                                    variant={
                                        selectedTags.includes(tagObj.tag) ? "default" : "outline"
                                    }
                                    className="cursor-pointer hover:bg-primary/80 flex items-center gap-2"
                                    onClick={() => handleTagToggle(tagObj.tag)}
                                >
                                    <span>{tagObj.tag}</span>
                                    <span className="text-xs opacity-70">({tagObj.count})</span>
                                    {selectedTags.includes(tagObj.tag) && (
                                        <Check className="h-3 w-3" />
                                    )}
                                </Badge>
                            ))}
                            {availableTags.length === 0 && (
                                <p className="text-sm text-muted-foreground">
                                    {t("form.empty.tags")}
                                </p>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Contact Exclusion */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        {t("form.sections.contact_selection")}
                    </CardTitle>
                    <CardDescription>{t("form.hints.contact_selection")}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder={t("form.placeholders.search_contacts")}
                            aria-label={t("form.fields.search_contacts")}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>

                    {loading ? (
                        <div className="text-center py-4">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                            <p className="mt-2 text-sm text-muted-foreground">
                                {t("form.loading.contacts")}
                            </p>
                        </div>
                    ) : (
                        <div className="max-h-64 overflow-y-auto space-y-2">
                            {contacts.map((contact) => {
                                const isExcluded = excludedContactIds.includes(contact.id)
                                const shouldBeIncluded = shouldContactBeIncluded(contact)
                                const buttonAction = getContactButtonAction(contact)
                                return (
                                    <div
                                        key={contact.id}
                                        className="flex items-center justify-between border border-gray-200 rounded-md p-2"
                                    >
                                        <div>
                                            <p className="font-medium">{contact.name}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {contact.email}
                                            </p>
                                            <div className="flex flex-wrap gap-1 mt-1">
                                                {contact.tags.map((tag) => (
                                                    <Badge
                                                        key={tag}
                                                        variant={
                                                            selectedTags.includes(tag) ? "default" : "outline"
                                                        }
                                                        className="text-xs"
                                                    >
                                                        {tag}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                        <Button
                                            size="sm"
                                            variant={buttonAction === "exclude" ? "destructive" : "outline"}
                                            onClick={() => handleContactExclude(contact.id)}
                                        >
                                            {buttonAction === "exclude" ? (
                                                <>
                                                    <X className="mr-1 h-4 w-4" />
                                                    {t("form.buttons.exclude")}
                                                </>
                                            ) : (
                                                <>
                                                    <Check className="mr-1 h-4 w-4" />
                                                    {t("form.buttons.include")}
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                )
                            })}
                            {contacts.length === 0 && (
                                <p className="text-sm text-muted-foreground">
                                    {t("form.empty.contacts")}
                                </p>
                            )}
                        </div>
                    )}

                    {/* Pagination */}
                    <div className="flex justify-between mt-4">
                        <Button
                            size="sm"
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                        >
                            {t("form.buttons.previous")}
                        </Button>
                        <Button
                            size="sm"
                            disabled={contacts.length < 20}
                            onClick={() => setCurrentPage((p) => p + 1)}
                        >
                            {t("form.buttons.next")}
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </>
    )
}
