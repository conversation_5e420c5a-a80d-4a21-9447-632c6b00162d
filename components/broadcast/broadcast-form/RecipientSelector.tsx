"use client"

import type {
  Broadcast<PERSON><PERSON><PERSON><PERSON><PERSON>ontact,
  TagWithCount,
} from "@/lib/services/broadcastRecipientsApi"
import { enhancedBroadcastFormLocales } from "@/localization/enhanced-broadcast-form"
import { useLocalization } from "@/localization/functions/client"
import { Check, Filter, List, Search, Tag, Users } from "lucide-react"
import { useState } from "react"
import { Badge } from "../../ui/badge"
import { Button } from "../../ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../ui/card"
import { Input } from "../../ui/input"
import { Label } from "../../ui/label"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "../../ui/tabs"
import { ContactRow } from "./ContactRow"

interface RecipientSelectorProps {
  selectedTags: string[]
  excludedContactIds: string[]
  includedContactIds: string[]
  availableTags: TagWithCount[]
  contacts: BroadcastRecipientContact[]
  searchTerm: string
  setSearchTerm: (term: string) => void
  handleTagToggle: (tagName: string) => void
  handleToggleContact: (
    contactId: string,
    action: "include" | "exclude",
  ) => void
  loading: boolean
  finalRecipientCount: number
  totalRecipients: number
  deterMineContactStatus: (contact: {
    id: string
    tags: string[]
  }) => "included" | "excluded"
  onResetAllSelections: () => void
  recipientCountLoading?: boolean
  recipientCountError?: string | null
}

export function RecipientSelector({
  selectedTags,
  excludedContactIds,
  availableTags,
  contacts,
  searchTerm,
  setSearchTerm,
  handleTagToggle,
  handleToggleContact,
  loading,
  finalRecipientCount,
  totalRecipients,
  deterMineContactStatus,
  recipientCountLoading = false,
  recipientCountError = null,
  onResetAllSelections,
}: RecipientSelectorProps) {
  const { t } = useLocalization(
    "enhanced-broadcast-form",
    enhancedBroadcastFormLocales,
  )

  const [currentPage, setCurrentPage] = useState(1)
  const [tagSearchTerm, setTagSearchTerm] = useState("")

  return (
    <div className="space-y-6">
      {/* Recipient Summary */}
      <Card>
        <CardContent className="pt-6">
          {recipientCountError ? (
            <div className="p-4 bg-red-50 rounded-lg">
              <div className="text-center">
                <p className="font-medium text-red-900 mb-2">
                  {t("form.errors.recipient_count_failed")}
                </p>
                <p className="text-sm text-red-700">{recipientCountError}</p>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-blue-900">
                    {t("form.recipient_summary.final_count", {
                      count: finalRecipientCount,
                    })}
                  </p>
                  <p className="text-sm text-blue-700">
                    {t("form.recipient_summary.all_contacts_count", {
                      count: totalRecipients,
                    })}
                  </p>
                  {excludedContactIds.length > 0 && (
                    <p className="text-sm text-red-600">
                      {t("form.recipient_summary.excluding_contacts", {
                        count: excludedContactIds.length,
                      })}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onResetAllSelections}
                  >
                    {t("form.buttons.reset_selections")}
                  </Button>
                  <Badge
                    variant="default"
                    className="text-lg px-3 py-1 flex items-center gap-2"
                  >
                    {recipientCountLoading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      finalRecipientCount
                    )}
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sub-tabs for Tag Selection and Contact Management */}
      <Tabs defaultValue="tags" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="tags" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            {t("form.sections.tag_selection")}
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <List className="h-4 w-4" />
            {t("form.sections.contact_selection")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tags" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5" />
                {t("form.sections.tag_selection")}
              </CardTitle>
              <CardDescription>{t("form.hints.tag_selection")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-base font-medium">
                    {t("form.fields.tags")}
                  </Label>
                  {selectedTags.length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        selectedTags.forEach((tag) => handleTagToggle(tag))
                      }
                      className="text-xs"
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Click tags to filter contacts. Multiple tags will include
                  contacts with any of the selected tags.
                </p>

                {/* Tag Search */}
                {availableTags.length > 6 && (
                  <div className="relative mb-4">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search tags..."
                      value={tagSearchTerm}
                      onChange={(e) => setTagSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                )}

                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {availableTags
                    .filter(
                      (tagObj) =>
                        tagSearchTerm === "" ||
                        tagObj.tag
                          .toLowerCase()
                          .includes(tagSearchTerm.toLowerCase()),
                    )
                    .map((tagObj) => (
                      <div
                        key={tagObj.tag}
                        className={`
                                                p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md
                                                ${
                                                  selectedTags.includes(
                                                    tagObj.tag,
                                                  )
                                                    ? "border-blue-500 bg-blue-50 shadow-sm"
                                                    : "border-gray-200 bg-white hover:border-gray-300"
                                                }
                                            `}
                        onClick={() => handleTagToggle(tagObj.tag)}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-sm truncate">
                            {tagObj.tag}
                          </span>
                          {selectedTags.includes(tagObj.tag) && (
                            <Check className="h-4 w-4 text-blue-600 flex-shrink-0" />
                          )}
                        </div>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-muted-foreground">
                            {tagObj.count} contacts
                          </span>
                          <Badge
                            variant={
                              selectedTags.includes(tagObj.tag)
                                ? "default"
                                : "outline"
                            }
                            className="text-xs"
                          >
                            {tagObj.count}
                          </Badge>
                        </div>
                      </div>
                    ))}
                </div>
                {availableTags.length === 0 && (
                  <div className="text-center py-12">
                    <Tag className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">
                      {t("form.empty.tags")}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t("form.sections.contact_selection")}
              </CardTitle>
              <CardDescription>
                {t("form.hints.contact_selection")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t("form.placeholders.search_contacts")}
                  aria-label={t("form.fields.search_contacts")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {loading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {t("form.loading.contacts")}
                  </p>
                </div>
              ) : (
                <div className="max-h-screen overflow-y-auto space-y-3">
                  {contacts.map((contact) => {
                    const contactStatus = deterMineContactStatus(contact)
                    return (
                      <ContactRow
                        key={contact.id}
                        contact={contact}
                        selectedTags={selectedTags}
                        buttonAction={
                          contactStatus === "included" ? "exclude" : "include"
                        }
                        onToggle={(action) =>
                          handleToggleContact(contact.id, action)
                        }
                        includeButtonText={t("form.buttons.include")}
                        excludeButtonText={t("form.buttons.exclude")}
                      />
                    )
                  })}
                  {contacts.length === 0 && (
                    <div className="text-center py-12">
                      <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground">
                        {t("form.empty.contacts")}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Pagination */}
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                >
                  {t("form.buttons.previous")}
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {currentPage}
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  disabled={contacts.length < 20}
                  onClick={() => setCurrentPage((p) => p + 1)}
                >
                  {t("form.buttons.next")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
