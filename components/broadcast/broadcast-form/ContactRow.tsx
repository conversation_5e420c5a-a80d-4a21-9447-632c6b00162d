"use client"

import { Bad<PERSON> } from "../../ui/badge"
import { <PERSON><PERSON> } from "../../ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar"
import { Check, X, Mail, User } from "lucide-react"
import type { BroadcastRecipientContact } from "@/lib/services/broadcastRecipientsApi"

interface ContactRowProps {
  contact: BroadcastRecipientContact
  selectedTags: string[]
  buttonAction: "include" | "exclude"
  onToggle: (action: "include" | "exclude") => void
  includeButtonText: string
  excludeButtonText: string
}

export function ContactRow({
  contact,
  selectedTags,
  buttonAction,
  onToggle,
  includeButtonText,
  excludeButtonText,
}: ContactRowProps) {
  const isIncluded = buttonAction === "exclude"

  return (
    <div
      className={`
                flex items-center gap-4 p-4 border rounded-lg transition-all duration-200 hover:shadow-md
                ${
                  isIncluded
                    ? "border-green-200 bg-green-50 hover:bg-green-100"
                    : "border-gray-200 bg-white hover:bg-gray-50"
                }
            `}
    >
      {/* Contact Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium text-gray-900 truncate">{contact.name}</h4>
          {isIncluded && (
            <div className="flex items-center gap-1 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-xs font-medium">Included</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
          <Mail className="h-3 w-3" />
          <span className="truncate">{contact.email}</span>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {contact.tags.map((tag) => (
            <Badge
              key={tag}
              variant={selectedTags.includes(tag) ? "default" : "outline"}
              className={`text-xs ${
                selectedTags.includes(tag)
                  ? "bg-blue-100 text-blue-800 border-blue-200"
                  : "bg-gray-100 text-gray-600 border-gray-200"
              }`}
            >
              {tag}
            </Badge>
          ))}
          {contact.tags.length === 0 && (
            <span className="text-xs text-gray-400 italic">No tags</span>
          )}
        </div>
      </div>

      {/* Action Button */}
      <Button
        size="sm"
        variant={buttonAction === "exclude" ? "destructive" : "default"}
        onClick={() => onToggle(buttonAction)}
        className={`
                    min-w-[100px] transition-all duration-200
                    ${
                      buttonAction === "exclude"
                        ? "hover:bg-red-600"
                        : "hover:bg-green-600 bg-green-500 text-white"
                    }
                `}
      >
        {buttonAction === "exclude" ? (
          <>
            <X className="mr-1 h-4 w-4" />
            {excludeButtonText}
          </>
        ) : (
          <>
            <Check className="mr-1 h-4 w-4" />
            {includeButtonText}
          </>
        )}
      </Button>
    </div>
  )
}
