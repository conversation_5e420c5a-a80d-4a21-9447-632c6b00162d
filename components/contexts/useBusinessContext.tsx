import { BusinessConfig } from "@/lib/config/business-config"
import React, { createContext, useContext, useState, ReactNode } from "react"

interface BusinessContextType {
  business: BusinessConfig
}

const BusinessContext = createContext<BusinessContextType | undefined>(
  undefined,
)

export const useBusinessContext = (): BusinessContextType => {
  const context = useContext(BusinessContext)
  if (!context) {
    throw new Error("useBusiness must be used within an BusinessProvider")
  }
  return context
}

export const BusinessProvider = ({
  children,
  business,
}: {
  children: ReactNode
  business: BusinessConfig
}) => {
  return (
    <BusinessContext.Provider value={{ business }}>
      {children}
    </BusinessContext.Provider>
  )
}
