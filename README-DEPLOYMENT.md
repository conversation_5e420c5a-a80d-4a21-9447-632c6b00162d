# CS AI App - Deployment Management

This project uses an advanced deployment system with git-based versioning, image preservation, and comprehensive management capabilities.

## Quick Start

### 🚀 Deploy New Version

```bash
# Build and deploy to staging
make local_stg_redeploy

# Deploy to production
make local_prod_redeploy
```

### ⚡ Quick Deploy (No Build)

```bash
# Deploy existing image to staging
make local_stg_redeploy_nobuild

# Deploy existing image to production
make local_prod_redeploy_nobuild
```

### 📋 Management Commands

```bash
# Show comprehensive help
make help

# List all available images
make list_images

# Show current status
make show_status

# View application logs
make show_logs_staging
make show_logs_production
```

### 🔄 Rollback to Previous Version

```bash
# List available versions
make list_images

# Rollback to specific version
make rollback_staging TAG=cs-ai-app:abc123f
make rollback_production TAG=cs-ai-app:abc123f
```

### 🧹 Maintenance

```bash
# Clean up old images (keeps latest 5)
make cleanup_old_images
```

## Key Features

### ✅ **Git-Based Versioning**

- Images tagged with commit hashes (e.g., `cs-ai-app:f0d6262`)
- Full traceability back to source code
- Easy identification of deployed versions

### ✅ **Image Preservation**

- Old images are kept for rollback capability
- No more lost working versions
- Controlled cleanup when needed

### ✅ **Optimized Docker Images**

- **44% smaller images** (967MB vs 1.72GB)
- Multi-stage builds with production-only files
- No source code in final image
- Security-hardened with non-root user

### ✅ **Zero-Downtime Deployments**

- Deploy without building for faster updates
- Rollback in seconds to any previous version
- Environment-specific configurations

### ✅ **Comprehensive Management**

- Single Makefile for all deployment operations
- Real-time status monitoring
- Integrated logging and troubleshooting

## Docker Image Optimization

The new Dockerfile provides significant improvements:

| Aspect         | Before           | After                 | Improvement  |
| -------------- | ---------------- | --------------------- | ------------ |
| **Image Size** | 1.72GB           | 967MB                 | 44% smaller  |
| **Security**   | Root user        | Non-root user         | ✅ Hardened  |
| **Content**    | Full source code | Production files only | ✅ Optimized |
| **Build**      | Single stage     | Multi-stage           | ✅ Efficient |

### What's Included in Production Image

- ✅ Next.js standalone build output
- ✅ Static assets (.next/static)
- ✅ Public files
- ✅ Production dependencies only
- ❌ Source code (excluded)
- ❌ Development dependencies (excluded)
- ❌ Build tools (excluded)

## Environment Configuration

The system supports two environments:

### Development (Default)

- File: `docker-compose.dev.yml`
- Environment: `.env.dev`
- Port: Configured in `.env.dev` (default: 3002)
- Purpose: Development and testing

### Production

- File: `docker-compose.prod.yml`
- Environment: `.env.prod`
- Port: Configured in `.env.prod` (default: 3002)
- Purpose: Production deployment
- Additional: Resource limits, optimized logging

## Migration from Old System

### Enhanced Makefile Commands

| Command                              | Description                             | Notes                |
| ------------------------------------ | --------------------------------------- | -------------------- |
| `make local_stg_redeploy`            | Build and deploy to staging             | Builds and deploys   |
| `make local_prod_redeploy`           | Build and deploy to production          | Builds and deploys   |
| `make local_stg_redeploy_nobuild`    | Deploy existing image to staging        | Deploy without build |
| `make local_prod_redeploy_nobuild`   | Deploy existing image to production     | Deploy without build |
| `make list_images`                   | List all available Docker images        | List images          |
| `make show_status`                   | Show comprehensive deployment status    | Enhanced status      |
| `make cleanup_old_images`            | Remove old images (keeps latest 5)      | Cleanup old images   |
| `make rollback_staging TAG=<tag>`    | Rollback staging to specific version    | Rollback staging     |
| `make rollback_production TAG=<tag>` | Rollback production to specific version | Rollback production  |
| `make show_logs_staging`             | Show staging application logs           | Monitor staging      |
| `make show_logs_production`          | Show production application logs        | Monitor production   |

### Key Improvements

- **Enhanced Makefile** with comprehensive deployment management
- **Interactive confirmations** for destructive operations
- **Better error handling** and validation
- **Comprehensive help** system with colored output
- **Real-time status** monitoring

## Troubleshooting

### Common Issues

**Image not found error:**

```bash
make list_images  # Check available images
make build_image  # Build new image if needed
```

**Container not starting:**

```bash
make show_logs_staging  # Check logs
make show_status        # Check overall status
```

**Port conflicts:**

```bash
# Check .env.dev or .env.prod file for DOCKER_EXPOSED_PORT
# Ensure port is not in use by other services
```

### Getting Help

```bash
# Show comprehensive help
make help

# Show current project status
make show_status
```

## Best Practices

1. **Always test in staging first**

   ```bash
   make local_stg_redeploy
   # Test your changes
   make local_prod_redeploy
   ```

2. **Use no-build deploys for quick restarts**

   ```bash
   make local_stg_redeploy_nobuild
   ```

3. **Keep track of working versions**

   ```bash
   make list_images  # Note working image tags
   ```

4. **Regular cleanup**

   ```bash
   make cleanup_old_images  # Remove old images periodically
   ```

5. **Monitor deployments**
   ```bash
   make show_status  # Check deployment status
   make show_logs_staging  # Monitor logs
   ```

## File Structure

- `Makefile` - Enhanced deployment management with comprehensive targets
- `Makefile` - Legacy deployment commands (still functional)
- `docker-start.sh` - Docker startup script
- `Dockerfile` - Optimized multi-stage build
- `docker-compose.yml` - Staging configuration
- `docker-compose.prod.yml` - Production configuration
- `.dockerignore` - Build optimization
- `README-DEPLOYMENT.md` - This documentation
