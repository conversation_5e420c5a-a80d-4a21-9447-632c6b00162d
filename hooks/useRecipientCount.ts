import { useEffect, useState, useCallback } from "react"
import {
  BroadcastRecipientsAPI,
  type CalculateRecipientsResponse,
} from "@/lib/services/broadcastRecipientsApi"

interface UseRecipientCountParams {
  selectedTags: string[]
  excludedContactIds: string[]
  includedContactIds: string[]
}

interface UseRecipientCountResult {
  recipientCount: number
  recipientData: CalculateRecipientsResponse | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useRecipientCount({
  selectedTags,
  excludedContactIds,
  includedContactIds,
}: UseRecipientCountParams): UseRecipientCountResult {
  const [recipientData, setRecipientData] =
    useState<CalculateRecipientsResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const calculateRecipients = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await BroadcastRecipientsAPI.CalculateRecipients({
        selectedTags,
        excludedContactIds,
        includedContactIds,
      }).request()

      setRecipientData(response)
    } catch (err) {
      console.error("Error calculating recipients:", err)
      setError(
        err instanceof Error ? err.message : "Failed to calculate recipients",
      )
      setRecipientData(null)
    } finally {
      setLoading(false)
    }
  }, [selectedTags, excludedContactIds, includedContactIds])

  // Recalculate when dependencies change
  useEffect(() => {
    calculateRecipients()
  }, [calculateRecipients])

  return {
    recipientCount: recipientData?.finalCount ?? 0,
    recipientData,
    loading,
    error,
    refetch: calculateRecipients,
  }
}
