#!/usr/bin/env python3
"""
Grafana Loki logging handler for Python microservices.
Sends logs to Grafana Loki in the same format as the main application.
"""

import os
import json
import time
import logging
import requests
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime
from queue import Queue, Empty


class GrafanaLokiHand<PERSON>(logging.Handler):
    """
    Custom logging handler that sends logs to Grafana Loki.
    Batches logs and sends them asynchronously to avoid blocking.
    """
    
    def __init__(
        self,
        endpoint: str,
        service_name: str = "python-microserver",
        environment: str = "development",
        api_key: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        batch_size: int = 100,
        flush_interval: float = 5.0,
        retry_attempts: int = 3,
        retry_delay: float = 1.0
    ):
        super().__init__()
        
        self.endpoint = endpoint.rstrip('/')
        self.service_name = service_name
        self.environment = environment
        self.api_key = api_key
        self.username = username
        self.password = password
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        # Log buffer and threading
        self.log_queue = Queue()
        self.shutdown_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._worker, daemon=True)
        self.worker_thread.start()
        
        # Setup authentication headers
        self.headers = {"Content-Type": "application/json"}
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
        elif self.username and self.password:
            import base64
            auth_string = f"{self.username}:{self.password}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            self.headers["Authorization"] = f"Basic {auth_b64}"
    
    def emit(self, record: logging.LogRecord) -> None:
        """Add log record to the queue for processing."""
        try:
            # Convert LogRecord to our format
            log_entry = self._format_log_entry(record)
            self.log_queue.put(log_entry, block=False)
        except Exception:
            # Don't let logging errors break the application
            self.handleError(record)
    
    def _format_log_entry(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Convert logging.LogRecord to our log entry format."""
        # Get the formatted message
        message = self.format(record)
        
        # Build metadata
        metadata = {
            "service": self.service_name,
            "environment": self.environment,
            "logger": record.name,
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, '__dict__'):
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'lineno', 
                              'funcName', 'created', 'msecs', 'relativeCreated',
                              'thread', 'threadName', 'processName', 'process',
                              'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    if isinstance(value, (str, int, float, bool)):
                        metadata[key] = str(value)
        
        # Handle exception info
        error_data = None
        if record.exc_info:
            error_data = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                # "stack": self.formatException(record.exc_info) if record.exc_info else None
            }
        
        return {
            "timestamp": datetime.fromtimestamp(record.created).isoformat() + "Z",
            "level": record.levelname.lower(),
            "message": message,
            "metadata": metadata,
            "error": error_data,
            "source": {
                "file": record.pathname,
                "line": record.lineno,
                "function": record.funcName
            }
        }
    
    def _worker(self) -> None:
        """Background worker that batches and sends logs to Grafana."""
        log_buffer = []
        last_flush = time.time()
        
        while not self.shutdown_event.is_set():
            try:
                # Try to get a log entry with timeout
                try:
                    log_entry = self.log_queue.get(timeout=1.0)
                    log_buffer.append(log_entry)
                    self.log_queue.task_done()
                except Empty:
                    pass
                
                # Check if we should flush
                current_time = time.time()
                should_flush = (
                    len(log_buffer) >= self.batch_size or
                    (log_buffer and current_time - last_flush >= self.flush_interval)
                )
                
                if should_flush:
                    self._send_logs(log_buffer)
                    log_buffer.clear()
                    last_flush = current_time
                    
            except Exception as e:
                # Log errors to stderr to avoid infinite loops
                print(f"GrafanaLokiHandler worker error: {e}", file=__import__('sys').stderr)
        
        # Flush remaining logs on shutdown
        if log_buffer:
            self._send_logs(log_buffer)
    
    def _send_logs(self, logs: List[Dict[str, Any]]) -> None:
        """Send a batch of logs to Grafana Loki."""
        if not logs:
            return
        
        streams = self._format_for_loki(logs)
        payload = {"streams": streams}
        
        for attempt in range(self.retry_attempts):
            try:
                response = requests.post(
                    f"{self.endpoint}/loki/api/v1/push",
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 204:
                    return  # Success
                else:
                    raise Exception(f"Loki API error: {response.status_code} {response.text}")
                    
            except Exception as e:
                if attempt == self.retry_attempts - 1:
                    print(f"Failed to send logs to Grafana after {self.retry_attempts} attempts: {e}", 
                          file=__import__('sys').stderr)
                else:
                    time.sleep(self.retry_delay * (attempt + 1))
    
    def _format_for_loki(self, logs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format logs for Loki streams."""
        # Group logs by labels
        stream_map = {}
        
        for log in logs:
            labels = self._build_labels(log)
            label_key = json.dumps(labels, sort_keys=True)
            
            if label_key not in stream_map:
                stream_map[label_key] = {
                    "stream": labels,
                    "values": []
                }
            
            # Convert timestamp to nanoseconds
            timestamp = datetime.fromisoformat(log["timestamp"].replace('Z', '+00:00'))
            timestamp_ns = int(timestamp.timestamp() * 1_000_000_000)
            
            # Format log line as JSON
            log_line = json.dumps({
                "message": log["message"],
                "timestamp": log["timestamp"],
                "level": log["level"],
                "metadata": log["metadata"],
                "error": log["error"],
                "source": log["source"]
            })
            
            stream_map[label_key]["values"].append([str(timestamp_ns), log_line])
        
        return list(stream_map.values())
    
    def _build_labels(self, log: Dict[str, Any]) -> Dict[str, str]:
        """Build Loki labels from log entry."""
        labels = {
            "level": log["level"],
            "service": self.service_name,
            "environment": self.environment,
        }
        
        # Add string metadata as labels
        if log.get("metadata"):
            for key, value in log["metadata"].items():
                if isinstance(value, str) and key not in ["message"]:
                    # Sanitize label key (Loki has restrictions)
                    clean_key = key.replace("-", "_").replace(".", "_")
                    labels[clean_key] = value
        
        return labels
    
    def close(self) -> None:
        """Shutdown the handler and flush remaining logs."""
        self.shutdown_event.set()
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=10)
        super().close()


def setup_grafana_logging(
    logger_name: str = None,
    level: str = "INFO"
) -> logging.Logger:
    """
    Setup Grafana logging for the microservice.
    
    Args:
        logger_name: Name of the logger (defaults to __name__)
        level: Logging level
    
    Returns:
        Configured logger instance
    """
    # Get configuration from environment
    grafana_endpoint = os.getenv('GRAFANA_LOKI_ENDPOINT')
    
    if not grafana_endpoint:
        # Return regular logger if Grafana is not configured
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, level.upper()))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    # Setup Grafana logging
    logger = logging.getLogger(logger_name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Remove existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Add console handler for development
    if os.getenv('NODE_ENV') == 'development':
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # Add Grafana handler
    grafana_handler = GrafanaLokiHandler(
        endpoint=grafana_endpoint,
        service_name="python-microserver",
        environment=os.getenv('NODE_ENV', 'development'),
        api_key=os.getenv('GRAFANA_API_KEY'),
        username=os.getenv('GRAFANA_USERNAME'),
        password=os.getenv('GRAFANA_PASSWORD'),
        batch_size=int(os.getenv('GRAFANA_BATCH_SIZE', '100')),
        flush_interval=float(os.getenv('GRAFANA_FLUSH_INTERVAL', '5000')) / 1000,
        retry_attempts=int(os.getenv('GRAFANA_RETRY_ATTEMPTS', '3')),
        retry_delay=float(os.getenv('GRAFANA_RETRY_DELAY', '1000')) / 1000
    )
    
    logger.addHandler(grafana_handler)
    
    return logger
