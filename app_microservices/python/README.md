# Broadcast Worker Service

This Python microservice processes WhatsApp broadcast messages from a RabbitMQ queue.

## Overview

The broadcast worker:

1. Connects to RabbitMQ and listens for messages on the `broadcast.send` queue
2. Processes each message by calling the main application's callback endpoint
3. Handles retries and error cases gracefully
4. Provides logging and monitoring capabilities

## Environment Variables

- `RABBITMQ_URL`: RabbitMQ connection URL (default: `amqp://admin:admin123@rabbitmq:5672`)

**Note**: The callback URL is now primarily read from each queue message's `callbackUrl` field. The `BROADCAST_SENDER_CALLBACK_URL` environment variable serves as a fallback for backward compatibility.

## Message Format

The worker expects messages in the following format:

```json
{
  "type": "broadcast.send",
  "payload": {
    "broadcastId": "string",
    "broadcastRecipientId": "string"
  },
  "context": {
    "userId": "string",
    "organizationId": "string"
  },
  "callbackUrl": "http://cs-ai-app:3000/api/v1/broadcast/send"
}
```

The `callbackUrl` field specifies where the worker should send the processed message. This allows different app instances to specify their own callback endpoints.

## Running

### With Docker Compose

```bash
docker-compose up broadcast-worker
```

### Standalone

```bash
cd app_microservices/python
pip install -r requirements.txt
python main.py
```

## Monitoring

The worker provides detailed logging and handles:

- Connection retries to RabbitMQ
- Message acknowledgment/rejection
- Graceful shutdown on SIGINT/SIGTERM
- Error handling and retry logic

## Health Checks

The main application provides a health check endpoint at `/api/health/queue` to monitor the message queue status.
