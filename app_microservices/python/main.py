#!/usr/bin/env python3
import os
import json
import time
import signal
import sys
import requests
import pika
from dotenv import load_dotenv
from typing import Dict, Any
from grafana_logger import setup_grafana_logging

# Load environment variables
load_dotenv()

# Configure logging with Grafana support
logger = setup_grafana_logging(__name__, os.getenv('LOG_LEVEL', 'INFO').upper())

class BroadcastWorker:
    def __init__(self):
        self.rabbitmq_url = os.getenv('RABBITMQ_URL_WORKER', '')

        if not self.rabbitmq_url:
            logger.error("RABBITMQ_URL_WORKER is not set in the environment.")
            sys.exit(1)

        self.connection = None
        self.channel = None
        self.should_stop = False

        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.should_stop = True

    def connect_to_rabbitmq(self):
        max_retries = 10
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                logger.info("Connecting to RabbitMQ", extra={
                    'attempt': attempt + 1,
                    'max_retries': max_retries,
                    'component': 'rabbitmq_client'
                })
                parameters = pika.URLParameters(self.rabbitmq_url)
                self.connection = pika.BlockingConnection(parameters)
                self.channel = self.connection.channel()
                self.channel.basic_qos(prefetch_count=1)
                logger.info("Successfully connected to RabbitMQ", extra={
                    'component': 'rabbitmq_client',
                    'attempt': attempt + 1
                })
                return True
            except Exception as e:
                logger.error("RabbitMQ connection failed", extra={
                    'attempt': attempt + 1,
                    'max_retries': max_retries,
                    'component': 'rabbitmq_client',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }, exc_info=True)
                if attempt < max_retries - 1:
                    logger.info("Retrying RabbitMQ connection", extra={
                        'retry_delay': retry_delay,
                        'component': 'rabbitmq_client'
                    })
                    time.sleep(retry_delay)
                else:
                    logger.error("Max retries reached for RabbitMQ connection", extra={
                        'component': 'rabbitmq_client'
                    })
                    return False

    def process_message(self, ch, method, _properties, body):
        start_time = time.time()
        message_id = None
        message_type = None

        try:
            message = json.loads(body.decode('utf-8'))
            message_id = message.get('id', 'unknown')
            message_type = message.get('type', 'unknown')

            logger.info("Processing message", extra={
                'message_id': message_id,
                'message_type': message_type,
                'component': 'message_processor'
            })

            if message.get('type') != 'broadcast.send':
                logger.error("Unsupported message type", extra={
                    'message_id': message_id,
                    'message_type': message_type,
                    'component': 'message_processor'
                })
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            callback_url = message.get('callbackUrl')
            token = message.get('internalSystemToken')

            if not callback_url:
                logger.error("Missing callbackUrl in message", extra={
                    'message_id': message_id,
                    'message_type': message_type,
                    'component': 'message_processor'
                })
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            if not token:
                logger.error("Missing internalSystemToken in message", extra={
                    'message_id': message_id,
                    'message_type': message_type,
                    'component': 'message_processor'
                })
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
                return

            headers = {
                'Content-Type': 'application/json',
                'X-Internal-System-Token': token
            }

            logger.info("Sending callback request", extra={
                'message_id': message_id,
                'callback_url': callback_url,
                'component': 'http_client'
            })

            response = requests.post(
                callback_url,
                json=message,
                headers=headers,
                timeout=30
            )

            duration = time.time() - start_time

            logger.info("Callback response received", extra={
                'message_id': message_id,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'component': 'http_client'
            })

            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    logger.info("Message processed successfully", extra={
                        'message_id': message_id,
                        'duration_ms': round(duration * 1000, 2),
                        'component': 'message_processor'
                    })
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                else:
                    logger.error("Callback returned error", extra={
                        'message_id': message_id,
                        'error': result.get('error'),
                        'component': 'message_processor'
                    })
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            else:
                logger.error("Callback request failed", extra={
                    'message_id': message_id,
                    'status_code': response.status_code,
                    'response_text': response.text[:500],  # Limit response text
                    'component': 'http_client'
                })
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        except json.JSONDecodeError as e:
            duration = time.time() - start_time
            logger.error("Invalid JSON in message", extra={
                'message_id': message_id,
                'duration_ms': round(duration * 1000, 2),
                'component': 'message_processor',
                'error_type': 'JSONDecodeError',
                'error_message': str(e)
            }, exc_info=True)
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            logger.error("HTTP request error", extra={
                'message_id': message_id,
                'duration_ms': round(duration * 1000, 2),
                'component': 'http_client',
                'error_type': type(e).__name__,
                'error_message': str(e)
            }, exc_info=True)
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        except Exception as e:
            duration = time.time() - start_time
            logger.error("Unexpected error processing message", extra={
                'message_id': message_id,
                'message_type': message_type,
                'duration_ms': round(duration * 1000, 2),
                'component': 'message_processor',
                'error_type': type(e).__name__,
                'error_message': str(e)
            }, exc_info=True)
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

    def start_consuming(self):
        try:
            logger.info("Starting message consumption", extra={
                'queue': 'broadcast.send',
                'component': 'consumer'
            })
            self.channel.basic_consume(
                queue='broadcast.send',
                on_message_callback=self.process_message
            )

            logger.info("Ready to process messages", extra={
                'component': 'consumer'
            })
            while not self.should_stop:
                self.connection.process_data_events(time_limit=1)

        except Exception as e:
            logger.error("Error while consuming messages", extra={
                'component': 'consumer',
                'error_type': type(e).__name__,
                'error_message': str(e)
            }, exc_info=True)
        finally:
            self.cleanup()

    def cleanup(self):
        try:
            logger.info("Starting cleanup process", extra={
                'component': 'worker'
            })
            if self.channel and self.channel.is_open:
                self.channel.stop_consuming()
                self.channel.close()
            if self.connection and self.connection.is_open:
                self.connection.close()
            logger.info("Graceful shutdown complete", extra={
                'component': 'worker'
            })
        except Exception as e:
            logger.error("Cleanup error", extra={
                'component': 'worker',
                'error_type': type(e).__name__,
                'error_message': str(e)
            }, exc_info=True)

    def run(self):
        logger.info("Broadcast Worker starting", extra={
            'service': 'python-microserver',
            'component': 'worker',
            'rabbitmq_url': self.rabbitmq_url.split('@')[0] + '@***'  # Hide credentials
        })

        logger.info("Worker configuration", extra={
            'component': 'worker',
            'grafana_enabled': bool(os.getenv('GRAFANA_LOKI_ENDPOINT')),
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'environment': os.getenv('NODE_ENV', 'development')
        })

        if not self.connect_to_rabbitmq():
            logger.fatal("Failed to connect to RabbitMQ, exiting", extra={
                'component': 'worker'
            })
            sys.exit(1)

        self.start_consuming()

def main():
    worker = BroadcastWorker()
    worker.run()

if __name__ == "__main__":
    print("Starting main function")
    main()
