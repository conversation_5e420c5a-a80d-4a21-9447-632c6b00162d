version: "3.8"

services:
  rabbitmq:
    container_name: cs-ai-rabbitmq-dev
    ports:
      - "5672:5672"
      - "15672:15672"
    env_file:
      - ../../.env.dev

  broadcast-worker:
    container_name: cs-ai-broadcast-worker-dev
    env_file:
      - ../../.env.dev
    extra_hosts:
      # On Linux, this maps 'host.docker.internal' to the special gateway IP for the Docker host.
      # This makes it possible to access services running on the host machine (e.g., Grafana, Loki, etc.)
      - "host.docker.internal:host-gateway"
    depends_on:
      rabbitmq:
        condition: service_healthy
