version: "3.8"

services:
  rabbitmq:
    container_name: cs-ai-rabbitmq-prod
    ports:
      - "5673:5672"
      - "15673:15672"
    env_file:
      - ../../.env.prod
    networks:
      - cs-ai-network-prod
    volumes:
      - rabbitmq_data_prod:/var/lib/rabbitmq

  broadcast-worker:
    container_name: cs-ai-broadcast-worker-prod
    env_file:
      - ../../.env.prod
    networks:
      - cs-ai-network-prod

networks:
  cs-ai-network-prod:
    name: cs-ai-network-prod
    external: true

volumes:
  rabbitmq_data_prod:
    name: cs-ai-rabbitmq-data-prod
