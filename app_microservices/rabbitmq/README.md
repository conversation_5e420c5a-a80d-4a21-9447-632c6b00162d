# RabbitMQ Development Environment

This directory contains a standalone RabbitMQ setup for development purposes, allowing you to run the message queue infrastructure independently from the main Next.js application.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │    RabbitMQ     │    │ Broadcast Worker│
│  (localhost)    │◄──►│  (Container)    │◄──►│  (Container)    │
│     :3000       │    │     :5672       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Make (optional, for convenience commands)

### 1. Setup External Network

```bash
make network
# or manually:
docker network create cs-ai-network
```

### 2. Start Services

```bash
# Start all services (RabbitMQ + Worker)
make up

# Or start only RabbitMQ
make up-rabbitmq

# Or start only the broadcast worker
make up-worker
```

### 3. Access Services

- **RabbitMQ Management UI**: http://localhost:15672
  - Username: `admin`
  - Password: `admin123`
- **RabbitMQ AMQP**: `amqp://admin:admin123@localhost:5672`

## 📋 Available Commands

| Command             | Description                      |
| ------------------- | -------------------------------- |
| `make network`      | Create external Docker network   |
| `make up`           | Start all services               |
| `make up-rabbitmq`  | Start only RabbitMQ              |
| `make up-worker`    | Start only broadcast worker      |
| `make down`         | Stop all services                |
| `make logs`         | Show logs for all services       |
| `make logs-rmq`     | Show RabbitMQ logs               |
| `make logs-worker`  | Show broadcast worker logs       |
| `make restart`      | Restart all services             |
| `make clean`        | Remove containers and volumes    |
| `make status`       | Show service status              |
| `make health`       | Check service health             |
| `make shell-rmq`    | Open shell in RabbitMQ container |
| `make shell-worker` | Open shell in worker container   |

## 🔧 Configuration

### Environment Variables

The broadcast worker reads from the main `.env` file in the project root. Key variables:

```env
# RabbitMQ Connection
RABBITMQ_URL=amqp://admin:admin123@localhost:5672

# Callback URL (for worker to call Next.js app)
BROADCAST_SENDER_CALLBACK_URL=http://host.docker.internal:3000/api/v1/broadcast/send
```

### Pre-configured Queues

The setup automatically creates:

- `broadcast.send` - Main queue for broadcast messages
- `broadcast.send.dlq` - Dead letter queue for failed messages
- `broadcast.exchange` - Main exchange
- `broadcast.dlx` - Dead letter exchange

## 🔗 Integration with Next.js App

### Development Setup

1. Start RabbitMQ services:

   ```bash
   cd app_microservices/rabbitmq
   make up
   ```

2. Start your Next.js app separately:

   ```bash
   npm run dev
   ```

3. Update your Next.js app's environment:
   ```env
   RABBITMQ_URL=amqp://admin:admin123@localhost:5672
   ```

### Network Configuration

- **External Network**: `cs-ai-network` allows communication between containers and host
- **Host Access**: Worker uses `host.docker.internal:3000` to reach Next.js app on host
- **Port Mapping**: RabbitMQ ports are exposed to host for direct access

## 🐛 Troubleshooting

### Common Issues

1. **Network not found**

   ```bash
   make network
   ```

2. **Port conflicts**
   - Check if ports 5672 or 15672 are in use
   - Modify port mappings in `docker-compose.yml` if needed

3. **Worker can't reach Next.js app**
   - Ensure Next.js is running on port 3000
   - Check `BROADCAST_SENDER_CALLBACK_URL` environment variable
   - Verify `host.docker.internal` resolves correctly

4. **Permission issues**
   ```bash
   sudo chown -R $USER:$USER .
   ```

### Logs and Debugging

```bash
# Check all logs
make logs

# Check specific service
make logs-rmq
make logs-worker

# Check health
make health

# Access container shell
make shell-rmq
make shell-worker
```

## 📊 Monitoring

### RabbitMQ Management UI

Access http://localhost:15672 to:

- Monitor queue lengths
- View message rates
- Check connection status
- Manage exchanges and bindings

### Health Checks

```bash
# Quick health check
make health

# Detailed status
make status

# Check from Next.js app
curl http://localhost:3000/api/health/queue
```

## 🔄 Development Workflow

1. **Start infrastructure**:

   ```bash
   make dev-setup
   ```

2. **Develop and test**:
   - Make changes to broadcast logic
   - Test queue integration
   - Monitor through management UI

3. **Clean up**:
   ```bash
   make dev-teardown
   ```

## 📁 File Structure

```
app_microservices/rabbitmq/
├── docker-compose.yml      # Service definitions
├── rabbitmq.conf          # RabbitMQ configuration
├── definitions.json       # Pre-configured queues/exchanges
├── Makefile               # Convenience commands
└── README.md              # This file
```

## 🔐 Security Notes

- Default credentials are for development only
- Change credentials for production use
- Consider using environment variables for sensitive data
- Network is isolated but accessible from host
