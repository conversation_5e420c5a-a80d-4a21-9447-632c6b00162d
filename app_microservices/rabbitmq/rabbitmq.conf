# RabbitMQ Configuration for Development

# Logging
log.console = true
log.console.level = info
log.file = false

# Management plugin
management.tcp.port = 15672
management.tcp.ip = 0.0.0.0

# AMQP port
listeners.tcp.default = 5672

# Memory and disk thresholds
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# Heartbeat
heartbeat = 60

# Default vhost and permissions
default_vhost = /
default_permissions.configure = .*
default_permissions.read = .*
default_permissions.write = .*

# Load definitions on startup (optional, keep if using definitions.json)
management.load_definitions = /etc/rabbitmq/definitions.json

# Queue settings
queue_master_locator = min-masters
