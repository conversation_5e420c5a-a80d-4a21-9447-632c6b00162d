version: "3.8"

services:
  rabbitmq:
    image: rabbitmq:3.12-management
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - ./definitions.json:/etc/rabbitmq/definitions.json:ro
    networks:
      - cs-ai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=false"
      - "com.docker.compose.project=cs-ai-microservices"

  broadcast-worker:
    build:
      context: ../python
      dockerfile: Dockerfile
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - cs-ai-network
    restart: unless-stopped
    depends_on:
      rabbitmq:
        condition: service_healthy
    volumes:
      - ../python:/app
    labels:
      - "com.docker.compose.project=cs-ai-microservices"

networks:
  cs-ai-network:
    name: cs-ai-network
    external: true

volumes:
  rabbitmq_data:
    name: cs-ai-rabbitmq-data
