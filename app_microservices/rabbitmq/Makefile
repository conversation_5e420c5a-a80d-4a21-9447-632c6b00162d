# RabbitMQ Makefile (DEV & PROD Support with Separate Recipes)

.PHONY: help \
        network-dev network-prod \
        up-dev up-prod up-rabbitmq-dev up-worker-dev \
        up-rabbitmq-prod up-worker-prod \
        down-dev down-prod \
        logs-dev logs-prod logs-rmq-dev logs-worker-dev logs-rmq-prod logs-worker-prod \
        restart-dev restart-prod \
        clean-dev clean-prod \
        status-dev status-prod \
        health-dev health-prod \
        shell-rmq-dev shell-worker-dev \
        shell-rmq-prod shell-worker-prod \
        dev-setup dev-teardown \
        prod-setup prod-teardown

# ========== HELP ==========

help:
	@echo "Available commands:"
	@echo ""
	@echo "  DEV environment:"
	@echo "    network-dev       - Create dev network"
	@echo "    up-dev            - Start all dev services"
	@echo "    up-rabbitmq-dev   - Start only RabbitMQ (dev)"
	@echo "    up-worker-dev     - Start only broadcast worker (dev)"
	@echo "    down-dev          - Stop dev services"
	@echo "    logs-dev          - Show logs (dev)"
	@echo "    restart-dev       - Restart dev services"
	@echo "    clean-dev         - Clean dev containers and volumes"
	@echo "    status-dev        - Show dev status"
	@echo "    health-dev        - Check dev service health"
	@echo "    shell-rmq-dev     - Open shell in RabbitMQ container (dev)"
	@echo "    shell-worker-dev  - Open shell in worker container (dev)"
	@echo "    dev-setup         - Dev quick setup"
	@echo "    dev-teardown      - Dev cleanup"
	@echo ""
	@echo "  PROD environment:"
	@echo "    network-prod      - Create prod network"
	@echo "    up-prod           - Start all prod services"
	@echo "    up-rabbitmq-prod  - Start only RabbitMQ (prod)"
	@echo "    up-worker-prod    - Start only broadcast worker (prod)"
	@echo "    down-prod         - Stop prod services"
	@echo "    logs-prod         - Show logs (prod)"
	@echo "    restart-prod      - Restart prod services"
	@echo "    clean-prod        - Clean prod containers and volumes"
	@echo "    status-prod       - Show prod status"
	@echo "    health-prod       - Check prod service health"
	@echo "    shell-rmq-prod    - Open shell in RabbitMQ container (prod)"
	@echo "    shell-worker-prod - Open shell in worker container (prod)"
	@echo "    prod-setup        - Production quick setup"
	@echo "    prod-teardown     - Production cleanup"

# ========== NETWORKS ==========

network-dev:
	@docker network inspect cs-ai-network >/dev/null 2>&1 || \
	docker network create cs-ai-network

network-prod:
	@docker network inspect cs-ai-network-prod >/dev/null 2>&1 || \
	docker network create cs-ai-network-prod

# ========== COMPOSE WRAPPERS ==========

define dc_dev
	docker compose -f docker-compose.override.yml -f docker-compose.dev.yml --env-file ../../.env.dev $(1)
endef

define dc_prod
	docker compose -f docker-compose.override.yml -f docker-compose.prod.yml --env-file ../../.env.prod $(1)
endef

# ========== START/STOP ==========

up-dev: network-dev
	$(call dc_dev,up -d)

up-prod: network-prod
	$(call dc_prod,up -d)

up-rabbitmq-dev: network-dev
	$(call dc_dev,up -d rabbitmq)

up-worker-dev: network-dev
	$(call dc_dev,up -d broadcast-worker)

up-rabbitmq-prod: network-prod
	$(call dc_prod,up -d rabbitmq)

up-worker-prod: network-prod
	$(call dc_prod,up -d broadcast-worker)

down-dev:
	$(call dc_dev,down)

down-prod:
	$(call dc_prod,down)

restart-dev:
	$(call dc_dev,restart)

restart-prod:
	$(call dc_prod,restart)

clean-dev:
	$(call dc_dev,down -v)
	@docker volume rm cs-ai-rabbitmq-data 2>/dev/null || true

clean-prod:
	$(call dc_prod,down -v)
	@docker volume rm cs-ai-rabbitmq-data-prod 2>/dev/null || true

# ========== LOGGING ==========

logs-dev:
	$(call dc_dev,logs -f)

logs-prod:
	$(call dc_prod,logs -f)

logs-rmq-dev:
	$(call dc_dev,logs -f rabbitmq)

logs-worker-dev:
	$(call dc_dev,logs -f broadcast-worker)

logs-rmq-prod:
	$(call dc_prod,logs -f rabbitmq)

logs-worker-prod:
	$(call dc_prod,logs -f broadcast-worker)

# ========== STATUS ==========

status-dev:
	$(call dc_dev,ps)

status-prod:
	$(call dc_prod,ps)

# ========== HEALTH ==========

health-dev:
	@echo "=== RabbitMQ Health (DEV) ==="
	@docker compose -f docker-compose.dev.yml --env-file ../../.env.dev exec rabbitmq rabbitmq-diagnostics ping || echo "RabbitMQ not healthy"
	@echo ""
	@echo "=== Container Status ==="
	$(call dc_dev,ps)

health-prod:
	@echo "=== RabbitMQ Health (PROD) ==="
	@docker compose -f docker-compose.prod.yml --env-file ../../.env.prod exec rabbitmq rabbitmq-diagnostics ping || echo "RabbitMQ not healthy"
	@echo ""
	@echo "=== Container Status ==="
	$(call dc_prod,ps)

# ========== SHELL ACCESS ==========

shell-rmq-dev:
	$(call dc_dev,exec rabbitmq bash)

shell-worker-dev:
	$(call dc_dev,exec broadcast-worker bash)

shell-rmq-prod:
	$(call dc_prod,exec rabbitmq bash)

shell-worker-prod:
	$(call dc_prod,exec broadcast-worker bash)

# ========== SHORTCUTS ==========

dev-setup: network-dev up-dev
	@echo "✅ Dev environment is ready!"
	@echo "🔗 RabbitMQ Management UI: http://localhost:15672"
	@echo "🚪 AMQP Port: localhost:5672"

dev-teardown: clean-dev
	@echo "🧹 Dev environment cleaned!"

prod-setup: network-prod up-prod
	@echo "✅ PROD environment is ready!"
	@echo "🔗 RabbitMQ Management UI: http://localhost:15673"
	@echo "🚪 AMQP Port: localhost:5673"

prod-teardown: clean-prod
	@echo "🧹 PROD environment cleaned!"
