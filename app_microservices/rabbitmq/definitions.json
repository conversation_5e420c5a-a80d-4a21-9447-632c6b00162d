{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [{"name": "admin", "password_hash": "lS9vnyQw3UF4zlCthlLEdJAEisKfub9n+SUHiW6o41Embgwn", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "internal_cluster_id", "value": "rabbitmq-cluster-id-cs-ai-dev"}], "policies": [], "queues": [{"name": "broadcast.send", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-retries": 3}}, {"name": "broadcast.send.dlq", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "broadcast.exchange", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "broadcast.dlx", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "broadcast.exchange", "vhost": "/", "destination": "broadcast.send", "destination_type": "queue", "routing_key": "send", "arguments": {}}, {"source": "broadcast.dlx", "vhost": "/", "destination": "broadcast.send.dlq", "destination_type": "queue", "routing_key": "send", "arguments": {}}]}